
---

## 3. 后端目录结构与模块设计

### 3.1 Rust后端目录结构

```
src-tauri/                             # Tauri后端根目录
├── Cargo.toml                         # 项目配置：依赖管理、构建配置、元数据信息、特性开关、优化设置、目标平台、发布配置
├── tauri.conf.json                    # Tauri配置：窗口设置、权限配置、构建选项、安全策略、更新机制、图标资源、平台特定设置
├── build.rs                           # 构建脚本：编译时代码生成、资源嵌入、条件编译、环境检查、依赖验证、优化配置、平台适配
├── src/                               # 源代码目录
│   ├── main.rs                        # 应用入口：Tauri应用初始化、窗口创建、菜单设置、事件监听、插件注册、错误处理、生命周期管理
│   ├── lib.rs                         # 库入口：模块声明、公共接口、类型导出、特征定义、宏定义、条件编译、文档注释
│   ├── commands/                      # Tauri命令模块
│   │   ├── mod.rs                     # 命令模块入口：命令注册、权限检查、错误处理、日志记录、性能监控、安全验证、类型转换
│   │   ├── chat.rs                    # 聊天命令：会话管理、消息发送、流式响应、历史查询、模型切换、配置更新、状态同步
│   │   ├── knowledge.rs               # 知识库命令：文档上传、向量化处理、搜索查询、知识库管理、统计信息、批量操作、数据导入导出
│   │   ├── model.rs                   # 模型命令：模型下载、加载卸载、配置管理、性能监控、版本控制、兼容性检查、资源管理
│   │   ├── multimodal.rs              # 多模态命令：图像处理、音频转换、视频分析、OCR识别、格式转换、批量处理、结果缓存
│   │   ├── network.rs                 # 网络命令：设备发现、连接管理、数据传输、状态同步、安全验证、性能监控、错误恢复
│   │   ├── plugin.rs                  # 插件命令：插件加载、配置管理、权限控制、生命周期、API调用、事件分发、安全沙箱
│   │   ├── system.rs                  # 系统命令：系统信息、性能监控、日志管理、配置读写、更新检查、诊断工具、资源清理
│   │   └── settings.rs                # 设置命令：配置读写、验证更新、默认值、导入导出、重置功能、变更通知、备份恢复
│   ├── services/                      # 业务服务层
│   │   ├── mod.rs                     # 服务模块入口：服务注册、依赖注入、生命周期管理、错误处理、日志记录、性能监控、资源管理
│   │   ├── chat_service.rs            # 聊天服务：会话管理、消息处理、模型调用、流式响应、历史存储、状态管理、错误恢复
│   │   ├── knowledge_service.rs       # 知识库服务：文档解析、向量化、搜索引擎、索引管理、缓存策略、批量处理、数据同步
│   │   ├── model_service.rs           # 模型服务：模型加载、推理调度、资源管理、性能优化、缓存策略、错误处理、监控统计
│   │   ├── multimodal_service.rs      # 多模态服务：媒体处理、格式转换、特征提取、结果缓存、批量队列、错误重试、进度跟踪
│   │   ├── network_service.rs         # 网络服务：P2P通信、设备发现、数据传输、连接管理、安全加密、状态同步、错误恢复
│   │   ├── plugin_service.rs          # 插件服务：插件管理、沙箱执行、API代理、事件系统、权限控制、生命周期、安全审计
│   │   ├── storage_service.rs         # 存储服务：数据持久化、缓存管理、文件操作、备份恢复、数据迁移、完整性检查、性能优化
│   │   └── system_service.rs          # 系统服务：系统监控、资源管理、日志服务、配置管理、更新服务、诊断工具、性能分析
│   ├── core/                          # 核心功能模块
│   │   ├── mod.rs                     # 核心模块入口：核心组件注册、初始化顺序、依赖关系、错误处理、日志配置、性能监控
│   │   ├── ai/                        # AI推理引擎
│   │   │   ├── mod.rs                 # AI模块入口：推理引擎初始化、模型管理、任务调度、资源分配、性能监控、错误处理
│   │   │   ├── inference.rs           # 推理引擎：模型加载、推理执行、批处理、流式输出、性能优化、内存管理、错误恢复
│   │   │   ├── models.rs              # 模型管理：模型注册、版本控制、兼容性检查、资源管理、缓存策略、热加载、监控统计
│   │   │   ├── tokenizer.rs           # 分词器：文本分词、编码解码、特殊标记、词汇表管理、性能优化、缓存机制、错误处理
│   │   │   ├── embedding.rs           # 向量化：文本向量化、批量处理、缓存管理、性能优化、维度管理、相似度计算、索引构建
│   │   │   └── pipeline.rs            # 推理管道：任务流水线、并发控制、资源调度、性能监控、错误处理、结果缓存、优化策略
│   │   ├── database/                  # 数据库模块
│   │   │   ├── mod.rs                 # 数据库模块入口：连接管理、事务控制、迁移管理、性能监控、错误处理、连接池、备份恢复
│   │   │   ├── sqlite.rs              # SQLite数据库：连接管理、查询执行、事务处理、索引优化、性能调优、备份恢复、数据迁移
│   │   │   ├── chroma.rs              # ChromaDB向量库：向量存储、相似度搜索、索引管理、批量操作、性能优化、数据同步、错误处理
│   │   │   ├── migrations.rs          # 数据库迁移：版本管理、结构变更、数据迁移、回滚机制、完整性检查、性能优化、错误恢复
│   │   │   └── cache.rs               # 缓存层：内存缓存、持久化缓存、缓存策略、过期管理、性能监控、数据一致性、错误处理
│   │   ├── network/                   # 网络通信模块
│   │   │   ├── mod.rs                 # 网络模块入口：网络初始化、协议注册、连接管理、安全配置、性能监控、错误处理、资源清理
│   │   │   ├── p2p.rs                 # P2P网络：节点发现、连接建立、数据传输、路由管理、NAT穿透、安全加密、性能优化
│   │   │   ├── discovery.rs           # 设备发现：广播发现、服务注册、状态同步、网络拓扑、连接质量、安全验证、错误恢复
│   │   │   ├── transfer.rs            # 数据传输：文件传输、断点续传、压缩加密、进度跟踪、错误重试、带宽控制、完整性验证
│   │   │   └── security.rs            # 网络安全：身份验证、数据加密、证书管理、权限控制、安全审计、攻击防护、合规检查
│   │   ├── plugin/                    # 插件系统
│   │   │   ├── mod.rs                 # 插件模块入口：插件框架初始化、API注册、安全沙箱、生命周期管理、事件系统、错误处理
│   │   │   ├── manager.rs             # 插件管理器：插件加载、卸载、更新、依赖管理、版本控制、冲突解决、性能监控、安全审计
│   │   │   ├── runtime.rs             # 插件运行时：沙箱执行、资源限制、API代理、事件分发、错误隔离、性能监控、安全控制
│   │   │   ├── api.rs                 # 插件API：接口定义、权限控制、参数验证、结果处理、错误传播、日志记录、性能统计
│   │   │   └── security.rs            # 插件安全：权限模型、沙箱隔离、代码审计、资源限制、安全策略、威胁检测、合规验证
│   │   └── utils/                     # 工具模块
│   │       ├── mod.rs                 # 工具模块入口：工具函数注册、公共接口、错误处理、性能优化、日志配置、测试辅助
│   │       ├── crypto.rs              # 加密工具：数据加密、哈希计算、数字签名、密钥管理、随机数生成、安全存储、完整性验证
│   │       ├── file.rs                # 文件工具：文件操作、路径处理、权限管理、监控变更、批量处理、压缩解压、安全检查
│   │       ├── config.rs              # 配置工具：配置读写、验证解析、默认值、环境变量、配置合并、热重载、版本管理
│   │       ├── logger.rs              # 日志工具：日志记录、级别控制、格式化、轮转管理、性能监控、错误追踪、审计日志
│   │       ├── metrics.rs             # 性能指标：指标收集、统计分析、性能监控、资源使用、趋势分析、告警机制、报告生成
│   │       └── error.rs               # 错误处理：错误定义、错误传播、错误恢复、日志记录、用户提示、调试信息、错误统计
│   └── types/                         # 类型定义
│       ├── mod.rs                     # 类型模块入口：类型导出、公共接口、序列化配置、验证规则、转换函数、文档注释
│       ├── api.rs                     # API类型：请求响应、错误类型、状态码、参数验证、序列化、版本兼容、文档生成
│       ├── config.rs                  # 配置类型：配置结构、默认值、验证规则、环境变量、类型转换、版本管理、文档说明
│       ├── database.rs                # 数据库类型：实体模型、查询参数、结果集、关系映射、索引定义、迁移脚本、性能优化
│       ├── network.rs                 # 网络类型：协议定义、消息格式、连接状态、传输参数、安全配置、性能指标、错误类型
│       └── plugin.rs                  # 插件类型：插件接口、配置结构、权限模型、事件类型、API定义、安全策略、版本信息
```

### 3.2 Tauri集成与命令系统

#### 3.2.1 Tauri应用初始化

**main.rs - 应用入口配置**
```rust
// src/main.rs
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{
    CustomMenuItem, Manager, Menu, MenuItem, Submenu, SystemTray, SystemTrayEvent,
    SystemTrayMenu, SystemTrayMenuItem, WindowBuilder, WindowUrl,
};
use std::sync::Arc;
use tokio::sync::Mutex;

mod commands;
mod services;
mod core;
mod types;

use commands::*;
use services::*;
use core::*;

// 应用状态管理
#[derive(Default)]
pub struct AppState {
    pub chat_service: Arc<Mutex<ChatService>>,
    pub knowledge_service: Arc<Mutex<KnowledgeService>>,
    pub model_service: Arc<Mutex<ModelService>>,
    pub multimodal_service: Arc<Mutex<MultimodalService>>,
    pub network_service: Arc<Mutex<NetworkService>>,
    pub plugin_service: Arc<Mutex<PluginService>>,
    pub system_service: Arc<Mutex<SystemService>>,
}

#[tokio::main]
async fn main() {
    // 初始化日志系统
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
        .init();

    // 创建应用状态
    let app_state = AppState::default();

    // 创建菜单
    let menu = create_app_menu();

    // 创建系统托盘
    let tray = create_system_tray();

    // 构建Tauri应用
    tauri::Builder::default()
        .manage(app_state)
        .menu(menu)
        .system_tray(tray)
        .on_system_tray_event(handle_system_tray_event)
        .setup(|app| {
            // 应用初始化
            setup_app(app)?;
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 聊天命令
            chat::create_session,
            chat::send_message,
            chat::get_sessions,
            chat::get_messages,
            chat::delete_session,
            chat::update_session_settings,

            // 知识库命令
            knowledge::create_knowledge_base,
            knowledge::upload_documents,
            knowledge::search_documents,
            knowledge::get_knowledge_bases,
            knowledge::delete_knowledge_base,
            knowledge::get_embedding_progress,

            // 模型命令
            model::get_available_models,
            model::download_model,
            model::load_model,
            model::unload_model,
            model::get_model_status,
            model::get_download_progress,

            // 多模态命令
            multimodal::process_image,
            multimodal::process_audio,
            multimodal::process_video,
            multimodal::get_processing_history,

            // 网络命令
            network::discover_devices,
            network::connect_device,
            network::share_resource,
            network::get_connection_status,

            // 插件命令
            plugin::get_installed_plugins,
            plugin::install_plugin,
            plugin::uninstall_plugin,
            plugin::configure_plugin,
            plugin::get_plugin_store,

            // 系统命令
            system::get_system_info,
            system::get_performance_metrics,
            system::check_for_updates,
            system::export_logs,

            // 设置命令
            settings::get_settings,
            settings::update_settings,
            settings::reset_settings,
            settings::export_settings,
            settings::import_settings,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// 创建应用菜单
fn create_app_menu() -> Menu {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let close = CustomMenuItem::new("close".to_string(), "关闭");
    let minimize = CustomMenuItem::new("minimize".to_string(), "最小化");

    let submenu = Submenu::new("文件", Menu::new().add_item(quit).add_item(close));
    let window_submenu = Submenu::new("窗口", Menu::new().add_item(minimize));

    Menu::new()
        .add_submenu(submenu)
        .add_submenu(window_submenu)
        .add_native_item(MenuItem::Copy)
        .add_native_item(MenuItem::Paste)
        .add_native_item(MenuItem::Cut)
        .add_native_item(MenuItem::SelectAll)
}

// 创建系统托盘
fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示");
    let hide = CustomMenuItem::new("hide".to_string(), "隐藏");

    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}

// 处理系统托盘事件
fn handle_system_tray_event(app: &tauri::AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick {
            position: _,
            size: _,
            ..
        } => {
            let window = app.get_window("main").unwrap();
            window.show().unwrap();
            window.set_focus().unwrap();
        }
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                }
                "hide" => {
                    let window = app.get_window("main").unwrap();
                    window.hide().unwrap();
                }
                _ => {}
            }
        }
        _ => {}
    }
}

// 应用初始化设置
fn setup_app(app: &mut tauri::App) -> Result<(), Box<dyn std::error::Error>> {
    // 创建主窗口
    let main_window = WindowBuilder::new(
        app,
        "main",
        WindowUrl::App("index.html".into())
    )
    .title("AI Studio")
    .inner_size(1200.0, 800.0)
    .min_inner_size(800.0, 600.0)
    .center()
    .build()?;

    // 初始化服务
    let app_handle = app.handle();
    tauri::async_runtime::spawn(async move {
        if let Err(e) = initialize_services(&app_handle).await {
            eprintln!("Failed to initialize services: {}", e);
        }
    });

    Ok(())
}

// 初始化所有服务
async fn initialize_services(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();

    // 初始化数据库
    let db_service = database::DatabaseService::new().await?;

    // 初始化各个服务
    let mut chat_service = state.chat_service.lock().await;
    *chat_service = ChatService::new(db_service.clone()).await?;
    drop(chat_service);

    let mut knowledge_service = state.knowledge_service.lock().await;
    *knowledge_service = KnowledgeService::new(db_service.clone()).await?;
    drop(knowledge_service);

    let mut model_service = state.model_service.lock().await;
    *model_service = ModelService::new().await?;
    drop(model_service);

    // 启动后台任务
    start_background_tasks(app).await?;

    Ok(())
}

// 启动后台任务
async fn start_background_tasks(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let app_handle = app.clone();

    // 性能监控任务
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            if let Err(e) = collect_performance_metrics(&app_handle).await {
                eprintln!("Failed to collect performance metrics: {}", e);
            }
        }
    });

    // 自动保存任务
    let app_handle = app.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 5分钟
        loop {
            interval.tick().await;
            if let Err(e) = auto_save_data(&app_handle).await {
                eprintln!("Failed to auto save data: {}", e);
            }
        }
    });

    Ok(())
}

// 性能指标收集
async fn collect_performance_metrics(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();
    let system_service = state.system_service.lock().await;

    let metrics = system_service.get_performance_metrics().await?;

    // 发送性能指标到前端
    app.emit_all("performance-metrics", &metrics)?;

    Ok(())
}

// 自动保存数据
async fn auto_save_data(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();

    // 保存聊天数据
    let chat_service = state.chat_service.lock().await;
    chat_service.auto_save().await?;
    drop(chat_service);

    // 保存知识库数据
    let knowledge_service = state.knowledge_service.lock().await;
    knowledge_service.auto_save().await?;
    drop(knowledge_service);

    Ok(())
}
```

#### 3.2.2 命令系统架构

**commands/mod.rs - 命令模块入口**
```rust
// src/commands/mod.rs
use tauri::{command, State};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::AppState;
use crate::types::api::{ApiResponse, ApiError};

pub mod chat;
pub mod knowledge;
pub mod model;
pub mod multimodal;
pub mod network;
pub mod plugin;
pub mod system;
pub mod settings;

// 命令结果类型
pub type CommandResult<T> = Result<ApiResponse<T>, ApiError>;

// 通用命令特征
#[async_trait::async_trait]
pub trait Command<T, R> {
    async fn execute(&self, params: T, state: &AppState) -> CommandResult<R>;
}

// 命令执行器
pub struct CommandExecutor;

impl CommandExecutor {
    pub async fn execute<T, R, C>(
        command: C,
        params: T,
        state: &AppState,
    ) -> CommandResult<R>
    where
        C: Command<T, R>,
        T: Send + Sync,
        R: Send + Sync,
    {
        // 执行前置检查
        Self::pre_execute_check(&params, state).await?;

        // 执行命令
        let result = command.execute(params, state).await;

        // 执行后置处理
        Self::post_execute_process(&result, state).await?;

        result
    }

    async fn pre_execute_check<T>(
        _params: &T,
        _state: &AppState,
    ) -> Result<(), ApiError> {
        // 权限检查
        // 参数验证
        // 资源检查
        Ok(())
    }

    async fn post_execute_process<R>(
        _result: &CommandResult<R>,
        _state: &AppState,
    ) -> Result<(), ApiError> {
        // 日志记录
        // 性能统计
        // 事件发送
        Ok(())
    }
}

// 命令装饰器宏
#[macro_export]
macro_rules! command_handler {
    ($func:ident, $params:ty, $return:ty) => {
        #[command]
        pub async fn $func(
            params: $params,
            state: State<'_, AppState>,
        ) -> Result<ApiResponse<$return>, ApiError> {
            let command = paste::paste! { [<$func:camel Command>]::new() };
            CommandExecutor::execute(command, params, &*state).await
        }
    };
}

// 错误处理宏
#[macro_export]
macro_rules! handle_service_error {
    ($result:expr) => {
        match $result {
            Ok(data) => Ok(ApiResponse::success(data)),
            Err(e) => {
                log::error!("Service error: {}", e);
                Err(ApiError::internal_error(e.to_string()))
            }
        }
    };
}

// 参数验证宏
#[macro_export]
macro_rules! validate_params {
    ($params:expr, $($field:ident: $validator:expr),*) => {
        $(
            if !$validator(&$params.$field) {
                return Err(ApiError::validation_error(
                    format!("Invalid parameter: {}", stringify!($field))
                ));
            }
        )*
    };
}
```

### 3.3 AI推理引擎模块

#### 3.3.1 推理引擎架构

AI Studio 的推理引擎采用模块化设计，支持多种AI模型和推理后端，提供统一的推理接口和高性能的推理能力。

**推理引擎架构图**
```
AI推理引擎架构:

┌─────────────────────────────────────────────────────────────┐
│                    推理引擎管理层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  任务调度器  │ │  资源管理器  │ │  缓存管理器  │ │ 监控器  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    模型抽象层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  模型接口   │ │  分词器接口  │ │  配置接口   │ │ 生命周期 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    推理后端层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ LLaMA.cpp   │ │   Candle    │ │ ONNX Runtime│ │ 自定义  │ │
│  │   后端      │ │    后端     │ │    后端     │ │  后端   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    CPU      │ │    GPU      │ │    Metal    │ │  其他   │ │
│  │   计算      │ │   计算      │ │   计算      │ │ 加速器  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 3.3.2 推理引擎实现

**core/ai/inference.rs - 推理引擎核心**
```rust
// src/core/ai/inference.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};

use crate::core::ai::models::{ModelManager, ModelInfo, ModelConfig};
use crate::core::ai::tokenizer::{Tokenizer, TokenizerManager};
use crate::types::api::{InferenceRequest, InferenceResponse, StreamResponse};

// 推理引擎主结构
pub struct InferenceEngine {
    model_manager: Arc<ModelManager>,
    tokenizer_manager: Arc<TokenizerManager>,
    task_scheduler: Arc<TaskScheduler>,
    resource_manager: Arc<ResourceManager>,
    cache_manager: Arc<CacheManager>,
    performance_monitor: Arc<PerformanceMonitor>,
    config: InferenceConfig,
}

// 推理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceConfig {
    pub max_concurrent_tasks: usize,
    pub max_sequence_length: usize,
    pub default_temperature: f32,
    pub default_top_p: f32,
    pub default_top_k: u32,
    pub cache_enabled: bool,
    pub cache_size_mb: usize,
    pub performance_monitoring: bool,
    pub gpu_enabled: bool,
    pub gpu_memory_fraction: f32,
}

impl Default for InferenceConfig {
    fn default() -> Self {
        Self {
            max_concurrent_tasks: 4,
            max_sequence_length: 4096,
            default_temperature: 0.7,
            default_top_p: 0.9,
            default_top_k: 40,
            cache_enabled: true,
            cache_size_mb: 512,
            performance_monitoring: true,
            gpu_enabled: true,
            gpu_memory_fraction: 0.8,
        }
    }
}

// 推理任务
#[derive(Debug, Clone)]
pub struct InferenceTask {
    pub id: String,
    pub model_id: String,
    pub request: InferenceRequest,
    pub priority: TaskPriority,
    pub created_at: std::time::Instant,
    pub timeout: Option<std::time::Duration>,
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl InferenceEngine {
    pub async fn new(config: InferenceConfig) -> Result<Self> {
        let model_manager = Arc::new(ModelManager::new().await?);
        let tokenizer_manager = Arc::new(TokenizerManager::new().await?);
        let task_scheduler = Arc::new(TaskScheduler::new(config.max_concurrent_tasks).await?);
        let resource_manager = Arc::new(ResourceManager::new(&config).await?);
        let cache_manager = Arc::new(CacheManager::new(config.cache_size_mb).await?);
        let performance_monitor = Arc::new(PerformanceMonitor::new().await?);

        Ok(Self {
            model_manager,
            tokenizer_manager,
            task_scheduler,
            resource_manager,
            cache_manager,
            performance_monitor,
            config,
        })
    }

    // 执行推理任务
    pub async fn infer(&self, request: InferenceRequest) -> Result<InferenceResponse> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let task = InferenceTask {
            id: task_id.clone(),
            model_id: request.model_id.clone(),
            request: request.clone(),
            priority: TaskPriority::Normal,
            created_at: std::time::Instant::now(),
            timeout: Some(std::time::Duration::from_secs(300)), // 5分钟超时
        };

        // 检查缓存
        if self.config.cache_enabled {
            if let Some(cached_response) = self.cache_manager.get(&request).await? {
                return Ok(cached_response);
            }
        }

        // 提交任务到调度器
        let response = self.task_scheduler.submit_task(task).await?;

        // 缓存结果
        if self.config.cache_enabled {
            self.cache_manager.put(&request, &response).await?;
        }

        Ok(response)
    }

    // 流式推理
    pub async fn infer_stream(
        &self,
        request: InferenceRequest,
    ) -> Result<tokio::sync::mpsc::Receiver<StreamResponse>> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let task = InferenceTask {
            id: task_id.clone(),
            model_id: request.model_id.clone(),
            request,
            priority: TaskPriority::High, // 流式任务优先级较高
            created_at: std::time::Instant::now(),
            timeout: Some(std::time::Duration::from_secs(600)), // 10分钟超时
        };

        self.task_scheduler.submit_stream_task(task).await
    }

    // 加载模型
    pub async fn load_model(&self, model_info: &ModelInfo) -> Result<()> {
        // 检查资源是否足够
        self.resource_manager.check_resources_for_model(model_info).await?;

        // 加载模型
        self.model_manager.load_model(model_info).await?;

        // 加载对应的分词器
        self.tokenizer_manager.load_tokenizer(&model_info.tokenizer_path).await?;

        log::info!("Model {} loaded successfully", model_info.id);
        Ok(())
    }

    // 卸载模型
    pub async fn unload_model(&self, model_id: &str) -> Result<()> {
        self.model_manager.unload_model(model_id).await?;
        self.tokenizer_manager.unload_tokenizer(model_id).await?;

        log::info!("Model {} unloaded successfully", model_id);
        Ok(())
    }

    // 获取性能指标
    pub async fn get_performance_metrics(&self) -> Result<PerformanceMetrics> {
        self.performance_monitor.get_metrics().await
    }

    // 获取模型状态
    pub async fn get_model_status(&self, model_id: &str) -> Result<ModelStatus> {
        self.model_manager.get_model_status(model_id).await
    }

    // 清理资源
    pub async fn cleanup(&self) -> Result<()> {
        self.task_scheduler.shutdown().await?;
        self.model_manager.cleanup().await?;
        self.cache_manager.clear().await?;
        Ok(())
    }
}

// 任务调度器
pub struct TaskScheduler {
    task_queue: Arc<Mutex<std::collections::BinaryHeap<InferenceTask>>>,
    running_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    max_concurrent: usize,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl TaskScheduler {
    pub async fn new(max_concurrent: usize) -> Result<Self> {
        let scheduler = Self {
            task_queue: Arc::new(Mutex::new(std::collections::BinaryHeap::new())),
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            max_concurrent,
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
        };

        // 启动调度循环
        scheduler.start_scheduler_loop().await;

        Ok(scheduler)
    }

    pub async fn submit_task(&self, task: InferenceTask) -> Result<InferenceResponse> {
        let (tx, rx) = tokio::sync::oneshot::channel();

        // 将任务添加到队列
        {
            let mut queue = self.task_queue.lock().await;
            queue.push(task);
        }

        // 等待任务完成
        rx.await.map_err(|e| anyhow!("Task execution failed: {}", e))
    }

    pub async fn submit_stream_task(
        &self,
        task: InferenceTask,
    ) -> Result<tokio::sync::mpsc::Receiver<StreamResponse>> {
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // 创建流式任务处理器
        let task_handle = tokio::spawn(async move {
            // 流式任务处理逻辑
            // ...
        });

        // 记录运行中的任务
        {
            let mut running_tasks = self.running_tasks.write().await;
            running_tasks.insert(task.id.clone(), task_handle);
        }

        Ok(rx)
    }

    async fn start_scheduler_loop(&self) {
        let task_queue = self.task_queue.clone();
        let running_tasks = self.running_tasks.clone();
        let max_concurrent = self.max_concurrent;
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = shutdown_signal.notified() => {
                        log::info!("Task scheduler shutting down");
                        break;
                    }
                    _ = tokio::time::sleep(tokio::time::Duration::from_millis(100)) => {
                        // 检查是否有可用的执行槽位
                        let running_count = {
                            let tasks = running_tasks.read().await;
                            tasks.len()
                        };

                        if running_count < max_concurrent {
                            // 从队列中取出任务
                            let task = {
                                let mut queue = task_queue.lock().await;
                                queue.pop()
                            };

                            if let Some(task) = task {
                                // 执行任务
                                let task_id = task.id.clone();
                                let running_tasks_clone = running_tasks.clone();

                                let handle = tokio::spawn(async move {
                                    // 执行推理任务
                                    if let Err(e) = execute_inference_task(task).await {
                                        log::error!("Task execution failed: {}", e);
                                    }

                                    // 从运行列表中移除
                                    let mut tasks = running_tasks_clone.write().await;
                                    tasks.remove(&task_id);
                                });

                                let mut tasks = running_tasks.write().await;
                                tasks.insert(task_id, handle);
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn shutdown(&self) -> Result<()> {
        self.shutdown_signal.notify_waiters();

        // 等待所有任务完成
        let handles: Vec<_> = {
            let mut tasks = self.running_tasks.write().await;
            tasks.drain().map(|(_, handle)| handle).collect()
        };

        for handle in handles {
            let _ = handle.await;
        }

        Ok(())
    }
}

// 执行推理任务
async fn execute_inference_task(task: InferenceTask) -> Result<InferenceResponse> {
    let start_time = std::time::Instant::now();

    // 获取模型实例
    // 执行推理
    // 处理结果
    // 记录性能指标

    let duration = start_time.elapsed();
    log::debug!("Task {} completed in {:?}", task.id, duration);

    // 返回模拟结果
    Ok(InferenceResponse {
        id: task.id,
        text: "Generated response".to_string(),
        tokens: vec![],
        finish_reason: "stop".to_string(),
        usage: Default::default(),
        model: task.model_id,
        created: chrono::Utc::now().timestamp(),
    })
}

// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_latency_ms: f64,
    pub tokens_per_second: f64,
    pub memory_usage_mb: f64,
    pub gpu_utilization: f64,
    pub cache_hit_rate: f64,
}

// 模型状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelStatus {
    pub id: String,
    pub loaded: bool,
    pub memory_usage_mb: f64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub total_requests: u64,
    pub average_latency_ms: f64,
}

// 资源管理器
pub struct ResourceManager {
    config: InferenceConfig,
    system_info: SystemInfo,
}

impl ResourceManager {
    pub async fn new(config: &InferenceConfig) -> Result<Self> {
        let system_info = SystemInfo::collect().await?;

        Ok(Self {
            config: config.clone(),
            system_info,
        })
    }

    pub async fn check_resources_for_model(&self, model_info: &ModelInfo) -> Result<()> {
        // 检查内存是否足够
        let required_memory = model_info.memory_requirements_mb;
        let available_memory = self.system_info.available_memory_mb;

        if required_memory > available_memory {
            return Err(anyhow!(
                "Insufficient memory: required {}MB, available {}MB",
                required_memory,
                available_memory
            ));
        }

        // 检查GPU资源
        if self.config.gpu_enabled && model_info.requires_gpu {
            if !self.system_info.gpu_available {
                return Err(anyhow!("GPU required but not available"));
            }
        }

        Ok(())
    }
}

// 系统信息
#[derive(Debug, Clone)]
pub struct SystemInfo {
    pub total_memory_mb: f64,
    pub available_memory_mb: f64,
    pub cpu_cores: usize,
    pub gpu_available: bool,
    pub gpu_memory_mb: Option<f64>,
}

impl SystemInfo {
    pub async fn collect() -> Result<Self> {
        // 收集系统信息的实现
        Ok(Self {
            total_memory_mb: 16384.0, // 示例值
            available_memory_mb: 8192.0,
            cpu_cores: 8,
            gpu_available: true,
            gpu_memory_mb: Some(8192.0),
        })
    }
}

// 缓存管理器
pub struct CacheManager {
    cache: Arc<RwLock<lru::LruCache<String, InferenceResponse>>>,
    max_size_mb: usize,
}

impl CacheManager {
    pub async fn new(max_size_mb: usize) -> Result<Self> {
        let cache_capacity = (max_size_mb * 1024 * 1024) / 1024; // 估算条目数
        let cache = Arc::new(RwLock::new(lru::LruCache::new(cache_capacity)));

        Ok(Self {
            cache,
            max_size_mb,
        })
    }

    pub async fn get(&self, request: &InferenceRequest) -> Result<Option<InferenceResponse>> {
        let cache_key = self.generate_cache_key(request);
        let cache = self.cache.read().await;
        Ok(cache.peek(&cache_key).cloned())
    }

    pub async fn put(&self, request: &InferenceRequest, response: &InferenceResponse) -> Result<()> {
        let cache_key = self.generate_cache_key(request);
        let mut cache = self.cache.write().await;
        cache.put(cache_key, response.clone());
        Ok(())
    }

    pub async fn clear(&self) -> Result<()> {
        let mut cache = self.cache.write().await;
        cache.clear();
        Ok(())
    }

    fn generate_cache_key(&self, request: &InferenceRequest) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        request.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

// 性能监控器
pub struct PerformanceMonitor {
    metrics: Arc<RwLock<PerformanceMetrics>>,
    start_time: std::time::Instant,
}

impl PerformanceMonitor {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                average_latency_ms: 0.0,
                tokens_per_second: 0.0,
                memory_usage_mb: 0.0,
                gpu_utilization: 0.0,
                cache_hit_rate: 0.0,
            })),
            start_time: std::time::Instant::now(),
        })
    }

    pub async fn get_metrics(&self) -> Result<PerformanceMetrics> {
        let metrics = self.metrics.read().await;
        Ok(metrics.clone())
    }

    pub async fn record_request(&self, duration: std::time::Duration, success: bool) -> Result<()> {
        let mut metrics = self.metrics.write().await;

        metrics.total_requests += 1;
        if success {
            metrics.successful_requests += 1;
        } else {
            metrics.failed_requests += 1;
        }

        // 更新平均延迟
        let latency_ms = duration.as_millis() as f64;
        metrics.average_latency_ms =
            (metrics.average_latency_ms * (metrics.total_requests - 1) as f64 + latency_ms)
            / metrics.total_requests as f64;

        Ok(())
    }
}
```

---

## 第四部分：核心功能模块

### 4.1 聊天功能模块

#### 4.1.1 聊天模块架构设计

AI Studio 的聊天功能模块是整个应用的核心，提供智能对话、多模态交互、RAG增强等功能。采用前后端分离架构，前端负责用户交互和界面渲染，后端负责AI推理和数据处理。

**聊天模块架构图：**
```
聊天功能模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端聊天界面                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  会话管理   │ │  消息列表   │ │  输入组件   │ │ 设置面板 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 会话列表   │ │ • 消息渲染   │ │ • 文本输入   │ │ • 模型  │ │
│  │ • 新建会话   │ │ • 流式显示   │ │ • 文件上传   │ │ • 参数  │ │
│  │ • 会话搜索   │ │ • 消息操作   │ │ • 语音输入   │ │ • 提示词│ │
│  │ • 会话分组   │ │ • 代码高亮   │ │ • 快捷键    │ │ • RAG   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端聊天服务                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  会话管理   │ │  消息处理   │ │  AI推理     │ │ RAG检索 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 会话CRUD   │ │ • 消息存储   │ │ • 模型加载   │ │ • 向量搜索│ │
│  │ • 权限控制   │ │ • 格式转换   │ │ • 推理执行   │ │ • 结果重排│ │
│  │ • 状态同步   │ │ • 内容过滤   │ │ • 流式输出   │ │ • 上下文融合│ │
│  │ • 数据备份   │ │ • 历史管理   │ │ • 性能监控   │ │ • 相关性计算│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │  缓存   │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 会话数据   │ │ • 向量索引   │ │ • 附件文件   │ │ • 会话缓存│ │
│  │ • 消息记录   │ │ • 语义搜索   │ │ • 模型文件   │ │ • 推理缓存│ │
│  │ • 用户配置   │ │ • 知识库    │ │ • 日志文件   │ │ • 结果缓存│ │
│  │ • 统计数据   │ │ • 相似度    │ │ • 备份文件   │ │ • 元数据 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.1.2 聊天功能核心实现

**前端聊天组件实现：**
```typescript
// stores/chat.ts - 聊天状态管理
import { defineStore } from 'pinia'
import { invoke } from '@tauri-apps/api/tauri'
import { listen } from '@tauri-apps/api/event'

export interface Message {
  id: string
  session_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  metadata?: {
    model?: string
    tokens?: number
    latency?: number
    attachments?: string[]
  }
}

export interface Session {
  id: string
  title: string
  model_id: string
  system_prompt?: string
  created_at: number
  updated_at: number
  message_count: number
  settings: {
    temperature: number
    max_tokens: number
    top_p: number
    frequency_penalty: number
    presence_penalty: number
    enable_rag: boolean
    knowledge_bases: string[]
  }
}

export const useChatStore = defineStore('chat', {
  state: () => ({
    sessions: [] as Session[],
    currentSessionId: null as string | null,
    messages: new Map<string, Message[]>(),
    isLoading: false,
    isStreaming: false,
    streamingMessage: null as Message | null,
  }),

  getters: {
    currentSession: (state) =>
      state.sessions.find(s => s.id === state.currentSessionId),

    currentMessages: (state) =>
      state.currentSessionId ? state.messages.get(state.currentSessionId) || [] : [],

    sessionCount: (state) => state.sessions.length,

    totalMessages: (state) => {
      let total = 0
      state.messages.forEach(msgs => total += msgs.length)
      return total
    }
  },

  actions: {
    // 初始化聊天模块
    async initialize() {
      try {
        // 加载会话列表
        const sessions = await invoke<Session[]>('chat_get_sessions')
        this.sessions = sessions

        // 加载每个会话的消息
        for (const session of sessions) {
          const messages = await invoke<Message[]>('chat_get_messages', {
            sessionId: session.id
          })
          this.messages.set(session.id, messages)
        }

        // 监听流式消息
        await listen<{session_id: string, chunk: string}>('chat_message_chunk', (event) => {
          this.handleMessageChunk(event.payload)
        })

        // 监听消息完成
        await listen<{session_id: string, message: Message}>('chat_message_complete', (event) => {
          this.handleMessageComplete(event.payload)
        })

      } catch (error) {
        console.error('Failed to initialize chat:', error)
        throw error
      }
    },

    // 发送消息
    async sendMessage(content: string, attachments?: string[]) {
      if (!this.currentSessionId) {
        throw new Error('No active session')
      }

      try {
        this.isLoading = true
        this.isStreaming = true

        // 创建用户消息
        const userMessage: Message = {
          id: crypto.randomUUID(),
          session_id: this.currentSessionId,
          role: 'user',
          content,
          timestamp: Date.now(),
          metadata: { attachments }
        }

        // 添加到消息列表
        const messages = this.messages.get(this.currentSessionId) || []
        messages.push(userMessage)
        this.messages.set(this.currentSessionId, [...messages])

        // 发送到后端
        await invoke('chat_send_message', {
          sessionId: this.currentSessionId,
          message: content,
          attachments: attachments || []
        })

      } catch (error) {
        console.error('Failed to send message:', error)
        this.isLoading = false
        this.isStreaming = false
        throw error
      }
    }
  }
})
```

#### 4.1.3 后端聊天服务实现

**Rust后端聊天服务：**
```rust
// src/services/chat_service.rs
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tauri::{AppHandle, Manager};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub session_id: String,
    pub role: String, // "user", "assistant", "system"
    pub content: String,
    pub timestamp: i64,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub title: String,
    pub model_id: String,
    pub system_prompt: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
    pub message_count: i32,
    pub settings: SessionSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionSettings {
    pub temperature: f32,
    pub max_tokens: i32,
    pub top_p: f32,
    pub frequency_penalty: f32,
    pub presence_penalty: f32,
    pub enable_rag: bool,
    pub knowledge_bases: Vec<String>,
}

pub struct ChatService {
    db: Arc<Mutex<crate::database::Database>>,
    ai_service: Arc<Mutex<crate::services::AIService>>,
    knowledge_service: Arc<Mutex<crate::services::KnowledgeService>>,
    app_handle: AppHandle,
}

impl ChatService {
    pub fn new(
        db: Arc<Mutex<crate::database::Database>>,
        ai_service: Arc<Mutex<crate::services::AIService>>,
        knowledge_service: Arc<Mutex<crate::services::KnowledgeService>>,
        app_handle: AppHandle,
    ) -> Self {
        Self {
            db,
            ai_service,
            knowledge_service,
            app_handle,
        }
    }

    // 创建新会话
    pub async fn create_session(&self, options: CreateSessionOptions) -> Result<Session, Box<dyn std::error::Error>> {
        let session = Session {
            id: Uuid::new_v4().to_string(),
            title: options.title.unwrap_or_else(|| "新对话".to_string()),
            model_id: options.model_id,
            system_prompt: options.system_prompt,
            created_at: Utc::now().timestamp_millis(),
            updated_at: Utc::now().timestamp_millis(),
            message_count: 0,
            settings: options.settings.unwrap_or_default(),
        };

        // 保存到数据库
        let db = self.db.lock().await;
        db.create_session(&session).await?;

        Ok(session)
    }

    // 发送消息
    pub async fn send_message(&self, request: SendMessageRequest) -> Result<(), Box<dyn std::error::Error>> {
        // 1. 保存用户消息
        let user_message = Message {
            id: Uuid::new_v4().to_string(),
            session_id: request.session_id.clone(),
            role: "user".to_string(),
            content: request.message.clone(),
            timestamp: Utc::now().timestamp_millis(),
            metadata: Some(serde_json::json!({
                "attachments": request.attachments
            })),
        };

        {
            let db = self.db.lock().await;
            db.save_message(&user_message).await?;
        }

        // 2. 获取会话设置
        let session = {
            let db = self.db.lock().await;
            db.get_session(&request.session_id).await?
        };

        // 3. 构建上下文
        let mut context = self.build_context(&request.session_id, &session).await?;

        // 4. RAG增强（如果启用）
        if session.settings.enable_rag && !session.settings.knowledge_bases.is_empty() {
            let rag_context = self.retrieve_knowledge(&request.message, &session.settings.knowledge_bases).await?;
            if !rag_context.is_empty() {
                context = format!("相关知识：\n{}\n\n{}", rag_context, context);
            }
        }

        // 5. 开始AI推理
        let ai_service = self.ai_service.clone();
        let app_handle = self.app_handle.clone();
        let session_id = request.session_id.clone();
        let db = self.db.clone();

        tokio::spawn(async move {
            match Self::generate_response(ai_service, context, session, app_handle.clone(), session_id.clone()).await {
                Ok(response) => {
                    // 保存AI回复
                    let ai_message = Message {
                        id: Uuid::new_v4().to_string(),
                        session_id: session_id.clone(),
                        role: "assistant".to_string(),
                        content: response.content,
                        timestamp: Utc::now().timestamp_millis(),
                        metadata: Some(serde_json::json!({
                            "model": response.model,
                            "tokens": response.tokens,
                            "latency": response.latency_ms
                        })),
                    };

                    if let Ok(db) = db.lock().await {
                        let _ = db.save_message(&ai_message).await;
                    }

                    // 发送完成事件
                    let _ = app_handle.emit_all("chat_message_complete", serde_json::json!({
                        "session_id": session_id,
                        "message": ai_message
                    }));
                }
                Err(e) => {
                    // 发送错误事件
                    let _ = app_handle.emit_all("chat_error", serde_json::json!({
                        "session_id": session_id,
                        "error": e.to_string()
                    }));
                }
            }
        });

        Ok(())
    }

    // 生成AI回复
    async fn generate_response(
        ai_service: Arc<Mutex<crate::services::AIService>>,
        context: String,
        session: Session,
        app_handle: AppHandle,
        session_id: String,
    ) -> Result<AIResponse, Box<dyn std::error::Error>> {
        let mut ai = ai_service.lock().await;

        // 设置推理参数
        let params = InferenceParams {
            temperature: session.settings.temperature,
            max_tokens: session.settings.max_tokens,
            top_p: session.settings.top_p,
            frequency_penalty: session.settings.frequency_penalty,
            presence_penalty: session.settings.presence_penalty,
        };

        // 开始流式推理
        let start_time = std::time::Instant::now();
        let mut response_content = String::new();

        ai.generate_stream(&context, params, |chunk| {
            response_content.push_str(&chunk);

            // 发送流式事件
            let _ = app_handle.emit_all("chat_message_chunk", serde_json::json!({
                "session_id": session_id,
                "chunk": chunk
            }));
        }).await?;

        let latency_ms = start_time.elapsed().as_millis() as u64;

        Ok(AIResponse {
            content: response_content,
            model: session.model_id,
            tokens: response_content.chars().count() as u32, // 简化的token计算
            latency_ms,
        })
    }

    // 构建对话上下文
    async fn build_context(&self, session_id: &str, session: &Session) -> Result<String, Box<dyn std::error::Error>> {
        let db = self.db.lock().await;
        let messages = db.get_recent_messages(session_id, 20).await?; // 获取最近20条消息

        let mut context = String::new();

        // 添加系统提示词
        if let Some(system_prompt) = &session.system_prompt {
            context.push_str(&format!("System: {}\n\n", system_prompt));
        }

        // 添加历史对话
        for message in messages {
            match message.role.as_str() {
                "user" => context.push_str(&format!("Human: {}\n\n", message.content)),
                "assistant" => context.push_str(&format!("Assistant: {}\n\n", message.content)),
                _ => {}
            }
        }

        Ok(context)
    }

    // 检索知识库
    async fn retrieve_knowledge(&self, query: &str, knowledge_bases: &[String]) -> Result<String, Box<dyn std::error::Error>> {
        let knowledge_service = self.knowledge_service.lock().await;

        let mut results = Vec::new();
        for kb_id in knowledge_bases {
            let search_results = knowledge_service.search(kb_id, query, 5).await?;
            results.extend(search_results);
        }

        // 按相关性排序并格式化
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        let context = results.into_iter()
            .take(3) // 取前3个最相关的结果
            .map(|r| r.content)
            .collect::<Vec<_>>()
            .join("\n\n");

        Ok(context)
    }
}

#[derive(Debug, Deserialize)]
pub struct CreateSessionOptions {
    pub title: Option<String>,
    pub model_id: String,
    pub system_prompt: Option<String>,
    pub settings: Option<SessionSettings>,
}

#[derive(Debug, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub message: String,
    pub attachments: Vec<String>,
}

#[derive(Debug)]
struct AIResponse {
    content: String,
    model: String,
    tokens: u32,
    latency_ms: u64,
}

#[derive(Debug)]
struct InferenceParams {
    temperature: f32,
    max_tokens: i32,
    top_p: f32,
    frequency_penalty: f32,
    presence_penalty: f32,
}

impl Default for SessionSettings {
    fn default() -> Self {
        Self {
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 0.9,
            frequency_penalty: 0.0,
            presence_penalty: 0.0,
            enable_rag: false,
            knowledge_bases: Vec::new(),
        }
    }
}
```

### 4.2 知识库模块

#### 4.2.1 知识库模块架构设计

知识库模块是AI Studio的核心功能之一，提供文档管理、智能解析、向量化存储、语义搜索等功能。采用分层架构设计，支持多种文档格式，提供高效的检索能力。

**知识库模块架构图：**
```
知识库模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端知识库界面                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  知识库管理  │ │  文档上传   │ │  搜索组件   │ │ 统计面板 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 创建知识库 │ │ • 文件选择   │ │ • 语义搜索   │ │ • 存储统计│ │
│  │ • 知识库列表 │ │ • 批量上传   │ │ • 关键词搜索 │ │ • 文档数量│ │
│  │ • 权限管理   │ │ • 进度显示   │ │ • 高级筛选   │ │ • 处理状态│ │
│  │ • 配置设置   │ │ • 格式验证   │ │ • 结果排序   │ │ • 性能指标│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端知识库服务                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  文档解析   │ │  内容处理   │ │  向量化     │ │ 索引管理 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • PDF解析    │ │ • 文本清理   │ │ • 文本向量化 │ │ • 向量索引│ │
│  │ • Word解析   │ │ • 智能分块   │ │ • 批量处理   │ │ • 全文索引│ │
│  │ • Excel解析  │ │ • 元数据提取 │ │ • 质量检查   │ │ • 增量更新│ │
│  │ • 图片OCR    │ │ • 格式转换   │ │ • 缓存管理   │ │ • 性能优化│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        存储与检索层                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │  缓存   │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 知识库元数据│ │ • 向量存储   │ │ • 原始文档   │ │ • 搜索缓存│ │
│  │ • 文档信息   │ │ • 语义搜索   │ │ • 处理结果   │ │ • 向量缓存│ │
│  │ • 用户权限   │ │ • 相似度计算 │ │ • 临时文件   │ │ • 元数据 │ │
│  │ • 处理日志   │ │ • 集合管理   │ │ • 备份文件   │ │ • 统计数据│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2.2 文档解析引擎实现

**多格式文档解析器：**
```rust
// src/services/document_parser.rs
use std::path::Path;
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentContent {
    pub text: String,
    pub metadata: DocumentMetadata,
    pub chunks: Vec<DocumentChunk>,
    pub images: Vec<ImageContent>,
    pub tables: Vec<TableContent>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub created_at: Option<i64>,
    pub modified_at: Option<i64>,
    pub page_count: Option<u32>,
    pub word_count: u32,
    pub language: Option<String>,
    pub format: String,
    pub size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChunk {
    pub id: String,
    pub content: String,
    pub start_pos: usize,
    pub end_pos: usize,
    pub chunk_type: ChunkType,
    pub metadata: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChunkType {
    Paragraph,
    Heading,
    List,
    Table,
    Code,
    Quote,
}

#[async_trait]
pub trait DocumentParser: Send + Sync {
    async fn parse(&self, file_path: &Path) -> Result<DocumentContent, Box<dyn std::error::Error>>;
    fn supported_extensions(&self) -> Vec<&'static str>;
    fn parser_name(&self) -> &'static str;
}

// PDF解析器实现
pub struct PDFParser {
    ocr_enabled: bool,
}

#[async_trait]
impl DocumentParser for PDFParser {
    async fn parse(&self, file_path: &Path) -> Result<DocumentContent, Box<dyn std::error::Error>> {
        use pdf_extract::extract_text;

        // 提取文本内容
        let text = extract_text(file_path)?;

        // 提取元数据
        let metadata = self.extract_pdf_metadata(file_path).await?;

        // 智能分块
        let chunks = self.chunk_text(&text).await?;

        // 如果启用OCR，处理图片
        let images = if self.ocr_enabled {
            self.extract_images_with_ocr(file_path).await?
        } else {
            Vec::new()
        };

        // 提取表格
        let tables = self.extract_tables(file_path).await?;

        Ok(DocumentContent {
            text,
            metadata,
            chunks,
            images,
            tables,
        })
    }

    fn supported_extensions(&self) -> Vec<&'static str> {
        vec!["pdf"]
    }

    fn parser_name(&self) -> &'static str {
        "PDF Parser"
    }
}
```

### 4.3 模型管理模块

#### 4.3.1 模型管理模块架构设计

模型管理模块负责AI模型的下载、安装、加载、配置和监控，支持多种推理引擎，提供统一的模型管理接口。

**模型管理模块架构图：**
```
模型管理模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端模型管理界面                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  模型商店   │ │  本地模型   │ │  下载管理   │ │ 性能监控 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 模型浏览   │ │ • 模型列表   │ │ • 下载队列   │ │ • 资源使用│ │
│  │ • 搜索筛选   │ │ • 模型信息   │ │ • 进度跟踪   │ │ • 推理性能│ │
│  │ • 版本选择   │ │ • 加载状态   │ │ • 断点续传   │ │ • 错误统计│ │
│  │ • 下载安装   │ │ • 配置管理   │ │ • 速度控制   │ │ • 历史记录│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端模型服务                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  下载管理   │ │  模型注册   │ │  推理引擎   │ │ 性能监控 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • HTTP下载   │ │ • 模型扫描   │ │ • Candle    │ │ • 资源监控│ │
│  │ • 分片下载   │ │ • 格式验证   │ │ • llama.cpp │ │ • 性能分析│ │
│  │ • 校验验证   │ │ • 元数据提取 │ │ • ONNX RT   │ │ • 基准测试│ │
│  │ • 错误重试   │ │ • 兼容性检查 │ │ • 动态加载   │ │ • 告警机制│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        存储与缓存层                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  文件系统   │ │  内存缓存   │ │  配置   │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 模型元数据 │ │ • 模型文件   │ │ • 模型权重   │ │ • 推理参数│ │
│  │ • 下载记录   │ │ • 配置文件   │ │ • 分词器    │ │ • 硬件配置│ │
│  │ • 性能数据   │ │ • 日志文件   │ │ • 中间结果   │ │ • 用户偏好│ │
│  │ • 使用统计   │ │ • 备份文件   │ │ • 缓存策略   │ │ • 安全策略│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.3.2 模型下载管理器实现

**分片下载管理器：**
```rust
// src/services/model_downloader.rs
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use serde::{Deserialize, Serialize};
use reqwest::Client;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadTask {
    pub id: String,
    pub model_id: String,
    pub url: String,
    pub file_path: String,
    pub total_size: u64,
    pub downloaded_size: u64,
    pub status: DownloadStatus,
    pub created_at: i64,
    pub updated_at: i64,
    pub chunks: Vec<DownloadChunk>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

pub struct ModelDownloader {
    client: Client,
    tasks: Arc<RwLock<std::collections::HashMap<String, DownloadTask>>>,
    concurrent_chunks: usize,
    chunk_size: u64,
    app_handle: tauri::AppHandle,
}

impl ModelDownloader {
    pub fn new(app_handle: tauri::AppHandle) -> Self {
        Self {
            client: Client::new(),
            tasks: Arc::new(RwLock::new(std::collections::HashMap::new())),
            concurrent_chunks: 4, // 并发下载块数
            chunk_size: 8 * 1024 * 1024, // 8MB per chunk
            app_handle,
        }
    }

    // 开始下载模型
    pub async fn start_download(&self, request: DownloadRequest) -> Result<String, Box<dyn std::error::Error>> {
        // 1. 获取文件信息
        let file_info = self.get_file_info(&request.url).await?;

        // 2. 创建下载任务
        let task_id = uuid::Uuid::new_v4().to_string();
        let chunks = self.create_chunks(file_info.size, self.chunk_size);

        let task = DownloadTask {
            id: task_id.clone(),
            model_id: request.model_id,
            url: request.url,
            file_path: request.file_path.clone(),
            total_size: file_info.size,
            downloaded_size: 0,
            status: DownloadStatus::Pending,
            created_at: chrono::Utc::now().timestamp_millis(),
            updated_at: chrono::Utc::now().timestamp_millis(),
            chunks,
        };

        // 3. 保存任务并开始下载
        {
            let mut tasks = self.tasks.write().await;
            tasks.insert(task_id.clone(), task);
        }

        // 4. 异步执行下载
        let downloader = self.clone();
        tokio::spawn(async move {
            if let Err(e) = downloader.execute_download(&task_id).await {
                eprintln!("Download failed: {}", e);
            }
        });

        Ok(task_id)
    }
}
```

### 4.4 多模态交互模块

#### 4.4.1 多模态模块架构设计

多模态交互模块支持图像、音频、视频等多种媒体格式的处理，提供OCR识别、语音转换、图像分析等功能。

**多模态模块架构图：**
```
多模态交互模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端多模态界面                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  文件上传   │ │  处理配置   │ │  结果展示   │ │ 历史记录 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 拖拽上传   │ │ • 处理类型   │ │ • 结果预览   │ │ • 处理历史│ │
│  │ • 格式检测   │ │ • 参数设置   │ │ • 文本提取   │ │ • 结果管理│ │
│  │ • 批量上传   │ │ • 质量选择   │ │ • 数据导出   │ │ • 统计分析│ │
│  │ • 进度显示   │ │ • 输出格式   │ │ • 错误提示   │ │ • 性能监控│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端多模态服务                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  图像处理   │ │  音频处理   │ │  视频处理   │ │ 格式转换 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • OCR识别    │ │ • ASR转录   │ │ • 帧提取    │ │ • 格式检测│ │
│  │ • 图像分析   │ │ • TTS合成   │ │ • 内容分析   │ │ • 编码转换│ │
│  │ • 对象检测   │ │ • 降噪处理   │ │ • 字幕生成   │ │ • 压缩优化│ │
│  │ • 场景理解   │ │ • 音频增强   │ │ • 摘要生成   │ │ • 质量调整│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        AI引擎与存储                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  AI模型     │ │  处理引擎   │ │  结果存储   │ │  缓存   │ │
│  │             │ │             │ │             │ │         │ │
│  │ • OCR模型    │ │ • OpenCV    │ │ • 原始文件   │ │ • 处理缓存│ │
│  │ • ASR模型    │ │ • FFmpeg    │ │ • 处理结果   │ │ • 模型缓存│ │
│  │ • 视觉模型   │ │ • Tesseract │ │ • 元数据    │ │ • 临时文件│ │
│  │ • 语音模型   │ │ • Whisper   │ │ • 日志记录   │ │ • 统计数据│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.4.2 图像处理引擎实现

**OCR文字识别服务：**
```rust
// src/services/ocr_service.rs
use std::path::Path;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OCRResult {
    pub text: String,
    pub confidence: f32,
    pub bounding_boxes: Vec<TextBox>,
    pub language: String,
    pub processing_time_ms: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextBox {
    pub text: String,
    pub confidence: f32,
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

pub struct OCRService {
    tesseract_path: String,
    temp_dir: String,
}

impl OCRService {
    pub fn new(tesseract_path: String, temp_dir: String) -> Self {
        Self {
            tesseract_path,
            temp_dir,
        }
    }

    // 执行OCR识别
    pub async fn recognize_text(&self, image_path: &Path, options: OCROptions) -> Result<OCRResult, Box<dyn std::error::Error>> {
        let start_time = std::time::Instant::now();

        // 1. 图像预处理
        let processed_image = self.preprocess_image(image_path).await?;

        // 2. 执行OCR
        let ocr_result = self.run_tesseract(&processed_image, &options).await?;

        let processing_time = start_time.elapsed().as_millis() as u64;

        Ok(OCRResult {
            text: ocr_result.text,
            confidence: ocr_result.confidence,
            bounding_boxes: ocr_result.bounding_boxes,
            language: options.language,
            processing_time_ms: processing_time,
        })
    }
}
```

### 4.5 网络功能模块

#### 4.5.1 网络模块架构设计

网络功能模块提供局域网设备发现、P2P通信、文件传输、资源共享等功能，支持跨设备协作和数据同步。

**网络模块架构图：**
```
网络功能模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端网络界面                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  设备发现   │ │  连接管理   │ │  文件传输   │ │ 资源共享 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 设备扫描   │ │ • 连接状态   │ │ • 上传下载   │ │ • 模型共享│ │
│  │ • 设备列表   │ │ • 连接历史   │ │ • 传输队列   │ │ • 知识库共享│ │
│  │ • 信任管理   │ │ • 安全验证   │ │ • 进度监控   │ │ • 配置同步│ │
│  │ • 网络状态   │ │ • 权限控制   │ │ • 断点续传   │ │ • 协作功能│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端网络服务                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  设备发现   │ │  P2P通信    │ │  文件传输   │ │ 安全管理 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • mDNS广播   │ │ • WebRTC    │ │ • 分片传输   │ │ • 身份验证│ │
│  │ • 服务注册   │ │ • TCP/UDP   │ │ • 压缩加密   │ │ • 数据加密│ │
│  │ • 状态同步   │ │ • NAT穿透   │ │ • 完整性校验 │ │ • 权限控制│ │
│  │ • 心跳检测   │ │ • 连接池    │ │ • 错误重试   │ │ • 审计日志│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.5.2 设备发现服务实现

**mDNS设备发现：**
```rust
// src/services/discovery_service.rs
use std::sync::Arc;
use tokio::sync::{RwLock, Mutex};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Device {
    pub id: String,
    pub name: String,
    pub device_type: DeviceType,
    pub ip_address: std::net::IpAddr,
    pub port: u16,
    pub version: String,
    pub capabilities: Vec<String>,
    pub last_seen: i64,
    pub status: DeviceStatus,
    pub trust_level: TrustLevel,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeviceType {
    AIStudio,
    Desktop,
    Mobile,
    Server,
    Unknown,
}

pub struct DiscoveryService {
    devices: Arc<RwLock<std::collections::HashMap<String, Device>>>,
    local_device: Arc<RwLock<Device>>,
    mdns_service: Arc<Mutex<Option<mdns::Service>>>,
    app_handle: tauri::AppHandle,
}

impl DiscoveryService {
    pub fn new(app_handle: tauri::AppHandle) -> Self {
        // 创建本地设备信息
        let local_device = Device {
            id: uuid::Uuid::new_v4().to_string(),
            name: gethostname::gethostname().to_string_lossy().to_string(),
            device_type: DeviceType::AIStudio,
            ip_address: Self::get_local_ip(),
            port: 8080,
            version: env!("CARGO_PKG_VERSION").to_string(),
            capabilities: vec![
                "chat".to_string(),
                "knowledge".to_string(),
                "model".to_string(),
                "multimodal".to_string(),
            ],
            last_seen: chrono::Utc::now().timestamp_millis(),
            status: DeviceStatus::Online,
            trust_level: TrustLevel::Trusted,
        };

        Self {
            devices: Arc::new(RwLock::new(std::collections::HashMap::new())),
            local_device: Arc::new(RwLock::new(local_device)),
            mdns_service: Arc::new(Mutex::new(None)),
            app_handle,
        }
    }

    // 启动设备发现服务
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 1. 启动mDNS服务
        self.start_mdns_service().await?;

        // 2. 开始设备扫描
        self.start_device_scan().await?;

        // 3. 启动心跳检测
        self.start_heartbeat().await?;

        Ok(())
    }

    fn get_local_ip() -> std::net::IpAddr {
        "127.0.0.1".parse().unwrap()
    }
}
```

### 4.6 插件系统模块

#### 4.6.1 插件系统架构设计

插件系统模块提供安全的插件运行环境，支持JavaScript和WebAssembly插件，提供丰富的API接口和权限控制机制。

**插件系统架构图：**
```
插件系统模块架构：

┌─────────────────────────────────────────────────────────────┐
│                        前端插件界面                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  插件商店   │ │  已安装插件  │ │  插件开发   │ │ 权限管理 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 插件浏览   │ │ • 插件列表   │ │ • 代码编辑   │ │ • 权限设置│ │
│  │ • 搜索筛选   │ │ • 启用禁用   │ │ • 调试工具   │ │ • 安全审计│ │
│  │ • 安装卸载   │ │ • 配置管理   │ │ • 测试运行   │ │ • 访问控制│ │
│  │ • 评价反馈   │ │ • 更新检查   │ │ • 打包发布   │ │ • 日志查看│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        后端插件服务                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  插件管理   │ │  运行时环境  │ │  API代理    │ │ 安全沙箱 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 生命周期   │ │ • JS引擎     │ │ • 接口封装   │ │ • 权限隔离│ │
│  │ • 依赖管理   │ │ • WASM运行时 │ │ • 参数验证   │ │ • 资源限制│ │
│  │ • 版本控制   │ │ • 内存管理   │ │ • 结果处理   │ │ • 代码审计│ │
│  │ • 热加载    │ │ • 异常处理   │ │ • 事件分发   │ │ • 威胁检测│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.6.2 插件运行时实现

**JavaScript插件运行时：**
```rust
// src/services/plugin_runtime.rs
use std::sync::Arc;
use tokio::sync::{RwLock, Mutex};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Plugin {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub entry_point: String,
    pub permissions: Vec<Permission>,
    pub status: PluginStatus,
    pub config: serde_json::Value,
    pub installed_at: i64,
    pub last_updated: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginStatus {
    Installed,
    Enabled,
    Disabled,
    Error,
    Updating,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Permission {
    ReadFiles,
    WriteFiles,
    NetworkAccess,
    ChatAccess,
    KnowledgeAccess,
    ModelAccess,
    SystemInfo,
    Notifications,
}

pub struct PluginRuntime {
    plugins: Arc<RwLock<std::collections::HashMap<String, Plugin>>>,
    js_runtime: Arc<Mutex<Option<deno_core::JsRuntime>>>,
    app_handle: tauri::AppHandle,
}

impl PluginRuntime {
    pub fn new(app_handle: tauri::AppHandle) -> Self {
        Self {
            plugins: Arc::new(RwLock::new(std::collections::HashMap::new())),
            js_runtime: Arc::new(Mutex::new(None)),
            app_handle,
        }
    }

    // 加载插件
    pub async fn load_plugin(&self, plugin_path: &str) -> Result<String, Box<dyn std::error::Error>> {
        // 实现插件加载逻辑
        Ok("plugin_id".to_string())
    }

    // 启用插件
    pub async fn enable_plugin(&self, plugin_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        // 实现插件启用逻辑
        Ok(())
    }
}
```

---

## 第五部分：数据层设计

### 5.1 SQLite关系型数据库

#### 5.1.1 数据库架构设计

AI Studio 采用 SQLite 作为主要的关系型数据库，负责存储结构化数据，包括用户配置、聊天记录、模型信息、系统日志等。

**SQLite数据库架构图：**
```
SQLite数据库架构：

┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 系统服务 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 会话管理   │ │ • 文档管理   │ │ • 模型管理   │ │ • 配置管理│ │
│  │ • 消息处理   │ │ • 索引管理   │ │ • 下载管理   │ │ • 日志管理│ │
│  │ • 历史记录   │ │ • 搜索记录   │ │ • 性能监控   │ │ • 用户管理│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ SQL查询
┌─────────────────────────────────────────────────────────────┐
│                        数据访问层                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  连接池     │ │  事务管理   │ │  查询优化   │ │ 缓存层  │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 连接复用   │ │ • ACID保证   │ │ • 索引优化   │ │ • 查询缓存│ │
│  │ • 并发控制   │ │ • 回滚机制   │ │ • 执行计划   │ │ • 结果缓存│ │
│  │ • 超时处理   │ │ • 锁管理    │ │ • 统计信息   │ │ • 元数据 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        SQLite存储引擎                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  核心表     │ │  索引表     │ │  日志表     │ │ 配置表  │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 会话数据   │ │ • 主键索引   │ │ • 操作日志   │ │ • 系统配置│ │
│  │ • 消息数据   │ │ • 外键索引   │ │ • 错误日志   │ │ • 用户配置│ │
│  │ • 模型数据   │ │ • 复合索引   │ │ • 性能日志   │ │ • 应用配置│ │
│  │ • 文档数据   │ │ • 全文索引   │ │ • 审计日志   │ │ • 缓存配置│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**选择理由：**
- **零配置**：无需安装和配置数据库服务器
- **高性能**：对于桌面应用的数据量，性能表现优异
- **可靠性**：ACID事务支持，数据安全可靠
- **跨平台**：完美支持Windows和macOS
- **轻量级**：占用资源少，适合桌面应用
- **嵌入式**：直接嵌入应用程序，无外部依赖

#### 5.1.2 数据库表结构设计

**核心表结构**
```sql
-- ===================================================
-- AI Studio 数据库表结构设计
-- 版本: v3.0 深度优化版
-- 创建日期: 2025-01-11
-- 平台支持: Windows & macOS 桌面应用
-- 技术栈: Vue3.5+Vite7.0+Tauri2.x+Rust+SQLite
-- ===================================================

-- 用户配置表
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT NOT NULL UNIQUE,           -- 配置键名
    value TEXT NOT NULL,                -- 配置值(JSON格式)
    category TEXT NOT NULL DEFAULT 'general', -- 配置分类
    description TEXT,                   -- 配置描述
    data_type TEXT NOT NULL DEFAULT 'string', -- 数据类型
    is_encrypted BOOLEAN DEFAULT FALSE, -- 是否加密存储
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_settings_key (key),
    INDEX idx_settings_category (category)
);

-- 聊天会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,                -- UUID格式的会话ID
    title TEXT NOT NULL,                -- 会话标题
    model_id TEXT NOT NULL,             -- 使用的AI模型ID
    system_prompt TEXT,                 -- 系统提示词
    temperature REAL DEFAULT 0.7,      -- 温度参数
    max_tokens INTEGER DEFAULT 2048,   -- 最大token数
    top_p REAL DEFAULT 0.9,            -- top_p参数
    frequency_penalty REAL DEFAULT 0.0, -- 频率惩罚
    presence_penalty REAL DEFAULT 0.0,  -- 存在惩罚
    enable_rag BOOLEAN DEFAULT FALSE,   -- 是否启用RAG
    knowledge_bases TEXT,               -- 关联知识库ID列表(JSON)
    message_count INTEGER DEFAULT 0,   -- 消息数量
    total_tokens INTEGER DEFAULT 0,    -- 总token消耗
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_archived BOOLEAN DEFAULT FALSE, -- 是否归档
    tags TEXT,                         -- 标签(JSON数组)

    INDEX idx_sessions_model (model_id),
    INDEX idx_sessions_created (created_at),
    INDEX idx_sessions_activity (last_activity),
    INDEX idx_sessions_archived (is_archived)
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,                -- UUID格式的消息ID
    session_id TEXT NOT NULL,          -- 所属会话ID
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')), -- 消息角色
    content TEXT NOT NULL,             -- 消息内容
    content_type TEXT DEFAULT 'text',  -- 内容类型(text/image/file)
    metadata TEXT,                     -- 元数据(JSON格式)
    token_count INTEGER DEFAULT 0,     -- token数量
    model_used TEXT,                   -- 使用的模型
    inference_time_ms INTEGER,         -- 推理耗时(毫秒)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,  -- 软删除标记
    parent_message_id TEXT,            -- 父消息ID(用于消息树)

    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES chat_messages(id),

    INDEX idx_messages_session (session_id),
    INDEX idx_messages_created (created_at),
    INDEX idx_messages_role (role),
    INDEX idx_messages_deleted (is_deleted)
);

-- 消息附件表
CREATE TABLE message_attachments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id TEXT NOT NULL,          -- 关联消息ID
    file_name TEXT NOT NULL,           -- 文件名
    file_path TEXT NOT NULL,           -- 文件路径
    file_size INTEGER NOT NULL,       -- 文件大小(字节)
    file_type TEXT NOT NULL,           -- 文件类型
    mime_type TEXT,                    -- MIME类型
    file_hash TEXT,                    -- 文件哈希值
    processed BOOLEAN DEFAULT FALSE,   -- 是否已处理
    processing_result TEXT,            -- 处理结果(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (message_id) REFERENCES chat_messages(id) ON DELETE CASCADE,

    INDEX idx_attachments_message (message_id),
    INDEX idx_attachments_type (file_type),
    INDEX idx_attachments_processed (processed)
);

-- AI模型表
CREATE TABLE ai_models (
    id TEXT PRIMARY KEY,               -- 模型ID
    name TEXT NOT NULL,                -- 模型名称
    display_name TEXT NOT NULL,       -- 显示名称
    description TEXT,                  -- 模型描述
    model_type TEXT NOT NULL,          -- 模型类型(llm/embedding/multimodal)
    architecture TEXT,                -- 模型架构(llama/mistral/qwen等)
    parameter_count TEXT,              -- 参数量(7B/13B/70B等)
    quantization TEXT,                 -- 量化类型(fp16/int8/int4等)
    file_path TEXT,                    -- 模型文件路径
    file_size INTEGER,                 -- 文件大小(字节)
    file_hash TEXT,                    -- 文件哈希值
    config_path TEXT,                  -- 配置文件路径
    tokenizer_path TEXT,               -- 分词器路径
    inference_engine TEXT NOT NULL,   -- 推理引擎(candle/llama_cpp/onnx)
    supported_features TEXT,           -- 支持的功能(JSON数组)
    performance_metrics TEXT,          -- 性能指标(JSON)
    hardware_requirements TEXT,        -- 硬件要求(JSON)
    license TEXT,                      -- 许可证
    author TEXT,                       -- 作者/组织
    version TEXT,                      -- 版本号
    download_url TEXT,                 -- 下载地址
    status TEXT DEFAULT 'available',   -- 状态(available/downloading/loading/loaded/error)
    is_local BOOLEAN DEFAULT FALSE,    -- 是否本地模型
    is_enabled BOOLEAN DEFAULT TRUE,   -- 是否启用
    download_progress REAL DEFAULT 0,  -- 下载进度(0-100)
    load_time_ms INTEGER,              -- 加载耗时
    memory_usage_mb INTEGER,           -- 内存占用(MB)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used DATETIME,                -- 最后使用时间
    usage_count INTEGER DEFAULT 0,     -- 使用次数

    INDEX idx_models_type (model_type),
    INDEX idx_models_status (status),
    INDEX idx_models_enabled (is_enabled),
    INDEX idx_models_local (is_local),
    INDEX idx_models_last_used (last_used)
);

-- 模型下载任务表
CREATE TABLE model_download_tasks (
    id TEXT PRIMARY KEY,               -- 任务ID
    model_id TEXT NOT NULL,            -- 模型ID
    download_url TEXT NOT NULL,        -- 下载地址
    file_path TEXT NOT NULL,           -- 保存路径
    total_size INTEGER NOT NULL,       -- 总大小
    downloaded_size INTEGER DEFAULT 0, -- 已下载大小
    status TEXT DEFAULT 'pending',     -- 状态(pending/downloading/paused/completed/failed/cancelled)
    progress REAL DEFAULT 0,           -- 进度(0-100)
    download_speed INTEGER DEFAULT 0,  -- 下载速度(bytes/s)
    error_message TEXT,                -- 错误信息
    retry_count INTEGER DEFAULT 0,     -- 重试次数
    chunks_info TEXT,                  -- 分片信息(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,               -- 开始时间
    completed_at DATETIME,             -- 完成时间

    FOREIGN KEY (model_id) REFERENCES ai_models(id),

    INDEX idx_download_model (model_id),
    INDEX idx_download_status (status),
    INDEX idx_download_created (created_at)
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,               -- 知识库ID
    name TEXT NOT NULL,                -- 知识库名称
    description TEXT,                  -- 描述
    embedding_model TEXT NOT NULL,     -- 向量化模型
    chunk_size INTEGER DEFAULT 1000,   -- 分块大小
    chunk_overlap INTEGER DEFAULT 200, -- 分块重叠
    collection_name TEXT NOT NULL,     -- ChromaDB集合名称
    document_count INTEGER DEFAULT 0,  -- 文档数量
    chunk_count INTEGER DEFAULT 0,     -- 分块数量
    total_size INTEGER DEFAULT 0,      -- 总大小(字节)
    index_status TEXT DEFAULT 'ready', -- 索引状态(building/ready/error)
    last_indexed DATETIME,             -- 最后索引时间
    settings TEXT,                     -- 设置(JSON)
    permissions TEXT,                  -- 权限设置(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT FALSE,   -- 是否公开
    is_active BOOLEAN DEFAULT TRUE,    -- 是否激活

    INDEX idx_kb_name (name),
    INDEX idx_kb_status (index_status),
    INDEX idx_kb_active (is_active),
    INDEX idx_kb_created (created_at)
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,               -- 文档ID
    knowledge_base_id TEXT NOT NULL,   -- 所属知识库ID
    file_name TEXT NOT NULL,           -- 文件名
    file_path TEXT NOT NULL,           -- 文件路径
    file_size INTEGER NOT NULL,       -- 文件大小
    file_type TEXT NOT NULL,           -- 文件类型
    file_hash TEXT NOT NULL,           -- 文件哈希值
    mime_type TEXT,                    -- MIME类型
    title TEXT,                        -- 文档标题
    author TEXT,                       -- 作者
    language TEXT DEFAULT 'zh-CN',     -- 语言
    page_count INTEGER,                -- 页数
    word_count INTEGER,                -- 字数
    content_preview TEXT,              -- 内容预览(前500字符)
    metadata TEXT,                     -- 元数据(JSON)
    processing_status TEXT DEFAULT 'pending', -- 处理状态(pending/processing/completed/failed)
    processing_progress REAL DEFAULT 0, -- 处理进度(0-100)
    processing_error TEXT,             -- 处理错误信息
    chunk_count INTEGER DEFAULT 0,     -- 分块数量
    indexed_at DATETIME,               -- 索引时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,  -- 软删除标记

    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,

    INDEX idx_docs_kb (knowledge_base_id),
    INDEX idx_docs_status (processing_status),
    INDEX idx_docs_hash (file_hash),
    INDEX idx_docs_type (file_type),
    INDEX idx_docs_deleted (is_deleted)
);

-- 文档分块表
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY,               -- 分块ID
    document_id TEXT NOT NULL,         -- 所属文档ID
    knowledge_base_id TEXT NOT NULL,   -- 所属知识库ID
    chunk_index INTEGER NOT NULL,      -- 分块索引
    content TEXT NOT NULL,             -- 分块内容
    content_hash TEXT NOT NULL,        -- 内容哈希值
    start_pos INTEGER,                 -- 开始位置
    end_pos INTEGER,                   -- 结束位置
    chunk_type TEXT DEFAULT 'text',    -- 分块类型(text/heading/list/table/code)
    metadata TEXT,                     -- 元数据(JSON)
    token_count INTEGER,               -- token数量
    embedding_vector_id TEXT,          -- 向量ID(ChromaDB中的ID)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,

    INDEX idx_chunks_doc (document_id),
    INDEX idx_chunks_kb (knowledge_base_id),
    INDEX idx_chunks_index (chunk_index),
    INDEX idx_chunks_hash (content_hash),
    INDEX idx_chunks_vector (embedding_vector_id)
);

-- 搜索历史表
CREATE TABLE search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    knowledge_base_id TEXT,            -- 知识库ID(可为空表示全局搜索)
    query TEXT NOT NULL,               -- 搜索查询
    search_type TEXT DEFAULT 'semantic', -- 搜索类型(semantic/keyword/hybrid)
    result_count INTEGER DEFAULT 0,    -- 结果数量
    search_time_ms INTEGER,            -- 搜索耗时
    results TEXT,                      -- 搜索结果(JSON)
    user_feedback INTEGER,             -- 用户反馈(1-5星)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id),

    INDEX idx_search_kb (knowledge_base_id),
    INDEX idx_search_query (query),
    INDEX idx_search_created (created_at)
);

-- 多模态处理任务表
CREATE TABLE multimodal_tasks (
    id TEXT PRIMARY KEY,               -- 任务ID
    task_type TEXT NOT NULL,           -- 任务类型(ocr/asr/tts/image_analysis/video_analysis)
    file_path TEXT NOT NULL,           -- 源文件路径
    file_name TEXT NOT NULL,           -- 文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    file_type TEXT NOT NULL,           -- 文件类型
    processing_options TEXT,           -- 处理选项(JSON)
    status TEXT DEFAULT 'pending',     -- 状态(pending/processing/completed/failed)
    progress REAL DEFAULT 0,           -- 进度(0-100)
    result_data TEXT,                  -- 结果数据(JSON)
    result_files TEXT,                 -- 结果文件路径(JSON数组)
    processing_time_ms INTEGER,        -- 处理耗时
    error_message TEXT,                -- 错误信息
    quality_score REAL,                -- 质量评分(0-1)
    confidence_score REAL,             -- 置信度(0-1)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,             -- 完成时间

    INDEX idx_multimodal_type (task_type),
    INDEX idx_multimodal_status (status),
    INDEX idx_multimodal_created (created_at)
);

-- 网络设备表
CREATE TABLE network_devices (
    id TEXT PRIMARY KEY,               -- 设备ID
    name TEXT NOT NULL,                -- 设备名称
    device_type TEXT NOT NULL,         -- 设备类型(ai_studio/desktop/mobile/server)
    ip_address TEXT NOT NULL,          -- IP地址
    port INTEGER NOT NULL,             -- 端口
    version TEXT,                      -- 版本号
    capabilities TEXT,                 -- 功能列表(JSON数组)
    status TEXT DEFAULT 'offline',     -- 状态(online/offline/connecting/connected/busy)
    trust_level TEXT DEFAULT 'unknown', -- 信任级别(unknown/trusted/blocked)
    last_seen DATETIME,                -- 最后在线时间
    connection_count INTEGER DEFAULT 0, -- 连接次数
    data_transferred INTEGER DEFAULT 0, -- 传输数据量(字节)
    public_key TEXT,                   -- 公钥(用于加密通信)
    device_info TEXT,                  -- 设备信息(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_devices_ip (ip_address),
    INDEX idx_devices_status (status),
    INDEX idx_devices_trust (trust_level),
    INDEX idx_devices_last_seen (last_seen)
);

-- 文件传输任务表
CREATE TABLE file_transfer_tasks (
    id TEXT PRIMARY KEY,               -- 任务ID
    device_id TEXT NOT NULL,           -- 目标设备ID
    transfer_type TEXT NOT NULL,       -- 传输类型(upload/download)
    file_path TEXT NOT NULL,           -- 文件路径
    file_name TEXT NOT NULL,           -- 文件名
    file_size INTEGER NOT NULL,       -- 文件大小
    transferred_size INTEGER DEFAULT 0, -- 已传输大小
    status TEXT DEFAULT 'pending',     -- 状态(pending/transferring/paused/completed/failed/cancelled)
    progress REAL DEFAULT 0,           -- 进度(0-100)
    transfer_speed INTEGER DEFAULT 0,  -- 传输速度(bytes/s)
    error_message TEXT,                -- 错误信息
    checksum TEXT,                     -- 文件校验和
    encryption_key TEXT,               -- 加密密钥
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,               -- 开始时间
    completed_at DATETIME,             -- 完成时间

    FOREIGN KEY (device_id) REFERENCES network_devices(id),

    INDEX idx_transfer_device (device_id),
    INDEX idx_transfer_status (status),
    INDEX idx_transfer_created (created_at)
);

-- 插件表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,               -- 插件ID
    name TEXT NOT NULL,                -- 插件名称
    version TEXT NOT NULL,             -- 版本号
    description TEXT,                  -- 描述
    author TEXT,                       -- 作者
    homepage TEXT,                     -- 主页
    repository TEXT,                   -- 代码仓库
    license TEXT,                      -- 许可证
    entry_point TEXT NOT NULL,         -- 入口文件
    manifest_path TEXT NOT NULL,       -- 清单文件路径
    install_path TEXT NOT NULL,        -- 安装路径
    file_size INTEGER,                 -- 文件大小
    permissions TEXT,                  -- 权限列表(JSON数组)
    dependencies TEXT,                 -- 依赖列表(JSON)
    config_schema TEXT,                -- 配置模式(JSON Schema)
    default_config TEXT,               -- 默认配置(JSON)
    user_config TEXT,                  -- 用户配置(JSON)
    status TEXT DEFAULT 'installed',   -- 状态(installed/enabled/disabled/error/updating)
    is_system BOOLEAN DEFAULT FALSE,   -- 是否系统插件
    is_verified BOOLEAN DEFAULT FALSE, -- 是否已验证
    download_count INTEGER DEFAULT 0,  -- 下载次数
    rating REAL DEFAULT 0,             -- 评分(0-5)
    review_count INTEGER DEFAULT 0,    -- 评价数量
    last_error TEXT,                   -- 最后错误
    performance_metrics TEXT,          -- 性能指标(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used DATETIME,                -- 最后使用时间

    INDEX idx_plugins_status (status),
    INDEX idx_plugins_author (author),
    INDEX idx_plugins_rating (rating),
    INDEX idx_plugins_last_used (last_used)
);

-- 插件API调用日志表
CREATE TABLE plugin_api_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plugin_id TEXT NOT NULL,           -- 插件ID
    api_name TEXT NOT NULL,            -- API名称
    method TEXT NOT NULL,              -- 方法名
    parameters TEXT,                   -- 参数(JSON)
    result TEXT,                       -- 结果(JSON)
    execution_time_ms INTEGER,         -- 执行时间
    success BOOLEAN DEFAULT TRUE,      -- 是否成功
    error_message TEXT,                -- 错误信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (plugin_id) REFERENCES plugins(id) ON DELETE CASCADE,

    INDEX idx_plugin_logs_plugin (plugin_id),
    INDEX idx_plugin_logs_api (api_name),
    INDEX idx_plugin_logs_created (created_at),
    INDEX idx_plugin_logs_success (success)
);

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL,               -- 日志级别(debug/info/warn/error/fatal)
    category TEXT NOT NULL,            -- 日志分类(system/chat/knowledge/model/network/plugin)
    message TEXT NOT NULL,             -- 日志消息
    details TEXT,                      -- 详细信息(JSON)
    source TEXT,                       -- 来源(模块名)
    user_id TEXT,                      -- 用户ID(如果适用)
    session_id TEXT,                   -- 会话ID(如果适用)
    request_id TEXT,                   -- 请求ID(用于追踪)
    stack_trace TEXT,                  -- 堆栈跟踪(错误时)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_logs_level (level),
    INDEX idx_logs_category (category),
    INDEX idx_logs_created (created_at),
    INDEX idx_logs_source (source),
    INDEX idx_logs_request (request_id)
);

-- 性能监控表
CREATE TABLE performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,         -- 指标名称
    metric_type TEXT NOT NULL,         -- 指标类型(counter/gauge/histogram/timer)
    value REAL NOT NULL,               -- 指标值
    unit TEXT,                         -- 单位
    tags TEXT,                         -- 标签(JSON)
    component TEXT,                    -- 组件名称
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_metrics_name (metric_name),
    INDEX idx_metrics_type (metric_type),
    INDEX idx_metrics_component (component),
    INDEX idx_metrics_created (created_at)
);

-- 用户反馈表
CREATE TABLE user_feedback (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    feedback_type TEXT NOT NULL,       -- 反馈类型(bug/feature/improvement/other)
    title TEXT NOT NULL,               -- 标题
    description TEXT NOT NULL,         -- 描述
    category TEXT,                     -- 分类
    priority TEXT DEFAULT 'medium',    -- 优先级(low/medium/high/critical)
    status TEXT DEFAULT 'open',        -- 状态(open/in_progress/resolved/closed)
    user_email TEXT,                   -- 用户邮箱
    system_info TEXT,                  -- 系统信息(JSON)
    attachments TEXT,                  -- 附件路径(JSON数组)
    developer_notes TEXT,              -- 开发者备注
    resolution TEXT,                   -- 解决方案
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_at DATETIME,              -- 解决时间

    INDEX idx_feedback_type (feedback_type),
    INDEX idx_feedback_status (status),
    INDEX idx_feedback_priority (priority),
    INDEX idx_feedback_created (created_at)
);

-- 数据库版本表
CREATE TABLE schema_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL UNIQUE,      -- 版本号
    description TEXT,                  -- 版本描述
    migration_sql TEXT,                -- 迁移SQL
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_versions_version (version)
);

-- 初始化版本记录
INSERT INTO schema_versions (version, description)
VALUES ('3.0.0', 'AI Studio 深度优化完整架构设计版数据库初始版本');
```

#### 5.1.3 数据库索引优化策略

**索引设计原则：**
```sql
-- ===================================================
-- 索引优化策略
-- ===================================================

-- 1. 主键索引（自动创建）
-- 所有表的主键都会自动创建唯一索引

-- 2. 外键索引（手动创建）
-- 为所有外键字段创建索引，提高JOIN性能

-- 3. 查询频繁字段索引
-- 为经常用于WHERE、ORDER BY的字段创建索引

-- 4. 复合索引（针对特定查询模式）
CREATE INDEX idx_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX idx_chunks_kb_doc ON document_chunks(knowledge_base_id, document_id);
CREATE INDEX idx_logs_category_level_created ON system_logs(category, level, created_at);
CREATE INDEX idx_metrics_component_name_created ON performance_metrics(component, metric_name, created_at);

-- 5. 部分索引（条件索引）
CREATE INDEX idx_messages_active ON chat_messages(session_id, created_at)
WHERE is_deleted = FALSE;

CREATE INDEX idx_models_available ON ai_models(model_type, status)
WHERE is_enabled = TRUE;

CREATE INDEX idx_docs_processing ON documents(knowledge_base_id, processing_status)
WHERE is_deleted = FALSE;

-- 6. 全文搜索索引
CREATE VIRTUAL TABLE chat_messages_fts USING fts5(
    content,
    content='chat_messages',
    content_rowid='rowid'
);

CREATE VIRTUAL TABLE documents_fts USING fts5(
    title,
    content_preview,
    content='documents',
    content_rowid='rowid'
);

-- 7. 触发器维护FTS索引
CREATE TRIGGER chat_messages_fts_insert AFTER INSERT ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;

CREATE TRIGGER chat_messages_fts_delete AFTER DELETE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
END;

CREATE TRIGGER chat_messages_fts_update AFTER UPDATE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;
```

#### 5.1.4 数据库连接与事务管理

**连接池配置：**
```rust
// src/database/connection_pool.rs
use sqlx::{SqlitePool, sqlite::SqlitePoolOptions};
use std::time::Duration;

pub struct DatabasePool {
    pool: SqlitePool,
}

impl DatabasePool {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
        let pool = SqlitePoolOptions::new()
            .max_connections(20)                    // 最大连接数
            .min_connections(5)                     // 最小连接数
            .acquire_timeout(Duration::from_secs(30)) // 获取连接超时
            .idle_timeout(Duration::from_secs(600))   // 空闲连接超时
            .max_lifetime(Duration::from_secs(1800))  // 连接最大生命周期
            .test_before_acquire(true)              // 获取前测试连接
            .connect(database_url)
            .await?;

        // 设置SQLite优化参数
        sqlx::query("PRAGMA journal_mode = WAL")
            .execute(&pool)
            .await?;

        sqlx::query("PRAGMA synchronous = NORMAL")
            .execute(&pool)
            .await?;

        sqlx::query("PRAGMA cache_size = 10000")
            .execute(&pool)
            .await?;

        sqlx::query("PRAGMA temp_store = MEMORY")
            .execute(&pool)
            .await?;

        sqlx::query("PRAGMA mmap_size = 268435456") // 256MB
            .execute(&pool)
            .await?;

        Ok(Self { pool })
    }

    pub fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }

    // 事务执行器
    pub async fn execute_transaction<F, R>(&self, f: F) -> Result<R, sqlx::Error>
    where
        F: for<'c> FnOnce(&mut sqlx::Transaction<'c, sqlx::Sqlite>) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, sqlx::Error>> + Send + 'c>>,
    {
        let mut tx = self.pool.begin().await?;
        let result = f(&mut tx).await?;
        tx.commit().await?;
        Ok(result)
    }
}
```

### 5.2 ChromaDB向量数据库

#### 5.2.1 ChromaDB架构设计

ChromaDB作为AI Studio的向量数据库，专门负责存储和检索文档的向量表示，支持高效的语义搜索和相似度计算。

**ChromaDB架构图：**
```
ChromaDB向量数据库架构：

┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  知识库服务  │ │  搜索服务   │ │  RAG服务    │ │ 分析服务 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 文档索引   │ │ • 语义搜索   │ │ • 上下文检索 │ │ • 相似度分析│ │
│  │ • 向量管理   │ │ • 混合搜索   │ │ • 结果重排   │ │ • 聚类分析│ │
│  │ • 集合管理   │ │ • 过滤查询   │ │ • 相关性计算 │ │ • 趋势分析│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓ ChromaDB API
┌─────────────────────────────────────────────────────────────┐
│                        ChromaDB核心                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  集合管理   │ │  向量索引   │ │  查询引擎   │ │ 元数据管理│ │
│  │             │ │             │ │             │ │         │ │
│  │ • 集合创建   │ │ • HNSW索引   │ │ • 相似度搜索 │ │ • 文档元数据│ │
│  │ • 集合配置   │ │ • 向量压缩   │ │ • 过滤查询   │ │ • 标签管理│ │
│  │ • 权限控制   │ │ • 增量更新   │ │ • 批量查询   │ │ • 统计信息│ │
│  │ • 备份恢复   │ │ • 索引优化   │ │ • 结果排序   │ │ • 版本控制│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                        存储层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  向量存储   │ │  元数据存储  │ │  索引存储   │ │ 配置存储 │ │
│  │             │ │             │ │             │ │         │ │
│  │ • 向量数据   │ │ • 文档信息   │ │ • HNSW图    │ │ • 集合配置│ │
│  │ • 嵌入向量   │ │ • 分块信息   │ │ • 倒排索引   │ │ • 索引配置│ │
│  │ • 压缩数据   │ │ • 标签数据   │ │ • 缓存数据   │ │ • 用户配置│ │
│  │ • 备份数据   │ │ • 统计数据   │ │ • 临时数据   │ │ • 系统配置│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 5.2.2 集合设计与配置

**知识库集合配置：**
```python
# ChromaDB集合配置示例
import chromadb
from chromadb.config import Settings

# ChromaDB客户端配置
client = chromadb.PersistentClient(
    path="./data/chromadb",
    settings=Settings(
        chroma_db_impl="duckdb+parquet",
        persist_directory="./data/chromadb",
        chroma_server_host="localhost",
        chroma_server_http_port=8000,
        anonymized_telemetry=False,
        allow_reset=True
    )
)

# 集合创建配置
collection_config = {
    "name": "knowledge_base_{kb_id}",
    "metadata": {
        "description": "AI Studio知识库向量集合",
        "version": "3.0",
        "embedding_model": "text-embedding-ada-002",
        "embedding_dimension": 1536,
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "language": "zh-CN",
        "created_at": "2025-01-11T00:00:00Z",
        "updated_at": "2025-01-11T00:00:00Z"
    },
    "embedding_function": embedding_function,
    "distance_metric": "cosine"  # cosine, l2, ip
}

# 创建集合
def create_knowledge_base_collection(kb_id: str, config: dict):
    collection_name = f"knowledge_base_{kb_id}"

    try:
        # 检查集合是否已存在
        existing_collections = client.list_collections()
        if collection_name in [c.name for c in existing_collections]:
            return client.get_collection(collection_name)

        # 创建新集合
        collection = client.create_collection(
            name=collection_name,
            metadata=config["metadata"],
            embedding_function=config["embedding_function"]
        )

        return collection

    except Exception as e:
        print(f"创建集合失败: {e}")
        raise
```

#### 5.2.3 向量化与索引策略

**文档向量化流程：**
```rust
// src/services/embedding_service.rs
use std::sync::Arc;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingRequest {
    pub text: String,
    pub model: String,
    pub chunk_id: String,
    pub metadata: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingResponse {
    pub embedding: Vec<f32>,
    pub dimension: usize,
    pub model: String,
    pub token_count: usize,
}

pub struct EmbeddingService {
    models: Arc<Mutex<std::collections::HashMap<String, Box<dyn EmbeddingModel>>>>,
    cache: Arc<Mutex<lru::LruCache<String, Vec<f32>>>>,
}

impl EmbeddingService {
    pub fn new() -> Self {
        Self {
            models: Arc::new(Mutex::new(std::collections::HashMap::new())),
            cache: Arc::new(Mutex::new(lru::LruCache::new(10000))), // 缓存10000个向量
        }
    }

    // 批量向量化
    pub async fn batch_embed(&self, requests: Vec<EmbeddingRequest>) -> Result<Vec<EmbeddingResponse>, Box<dyn std::error::Error>> {
        let mut responses = Vec::new();

        // 按模型分组
        let mut model_groups: std::collections::HashMap<String, Vec<EmbeddingRequest>> = std::collections::HashMap::new();
        for request in requests {
            model_groups.entry(request.model.clone()).or_insert_with(Vec::new).push(request);
        }

        // 并行处理不同模型
        let mut handles = Vec::new();
        for (model_name, model_requests) in model_groups {
            let service = self.clone();
            let handle = tokio::spawn(async move {
                service.process_model_batch(model_name, model_requests).await
            });
            handles.push(handle);
        }

        // 收集结果
        for handle in handles {
            let batch_responses = handle.await??;
            responses.extend(batch_responses);
        }

        Ok(responses)
    }

    // 处理单个模型的批次
    async fn process_model_batch(&self, model_name: String, requests: Vec<EmbeddingRequest>) -> Result<Vec<EmbeddingResponse>, Box<dyn std::error::Error>> {
        let models = self.models.lock().await;
        let model = models.get(&model_name).ok_or("Model not found")?;

        let mut responses = Vec::new();
        let batch_size = 32; // 批处理大小

        for chunk in requests.chunks(batch_size) {
            let texts: Vec<String> = chunk.iter().map(|r| r.text.clone()).collect();
            let embeddings = model.embed_batch(&texts).await?;

            for (request, embedding) in chunk.iter().zip(embeddings.iter()) {
                // 缓存结果
                let cache_key = format!("{}:{}", model_name, self.hash_text(&request.text));
                {
                    let mut cache = self.cache.lock().await;
                    cache.put(cache_key, embedding.clone());
                }

                responses.push(EmbeddingResponse {
                    embedding: embedding.clone(),
                    dimension: embedding.len(),
                    model: model_name.clone(),
                    token_count: self.count_tokens(&request.text),
                });
            }
        }

        Ok(responses)
    }

    // 文本哈希
    fn hash_text(&self, text: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        text.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    // 简单的token计数
    fn count_tokens(&self, text: &str) -> usize {
        // 简化实现，实际应该使用对应模型的tokenizer
        text.split_whitespace().count()
    }
}

// 向量化模型trait
#[async_trait::async_trait]
pub trait EmbeddingModel: Send + Sync {
    async fn embed(&self, text: &str) -> Result<Vec<f32>, Box<dyn std::error::Error>>;
    async fn embed_batch(&self, texts: &[String]) -> Result<Vec<Vec<f32>>, Box<dyn std::error::Error>>;
    fn dimension(&self) -> usize;
    fn model_name(&self) -> &str;
}

// OpenAI嵌入模型实现
pub struct OpenAIEmbeddingModel {
    client: reqwest::Client,
    api_key: String,
    model: String,
    dimension: usize,
}

#[async_trait::async_trait]
impl EmbeddingModel for OpenAIEmbeddingModel {
    async fn embed(&self, text: &str) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
        let response = self.client
            .post("https://api.openai.com/v1/embeddings")
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&serde_json::json!({
                "input": text,
                "model": self.model
            }))
            .send()
            .await?;

        let result: serde_json::Value = response.json().await?;
        let embedding = result["data"][0]["embedding"]
            .as_array()
            .ok_or("Invalid response format")?
            .iter()
            .map(|v| v.as_f64().unwrap_or(0.0) as f32)
            .collect();

        Ok(embedding)
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<Vec<Vec<f32>>, Box<dyn std::error::Error>> {
        let response = self.client
            .post("https://api.openai.com/v1/embeddings")
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&serde_json::json!({
                "input": texts,
                "model": self.model
            }))
            .send()
            .await?;

        let result: serde_json::Value = response.json().await?;
        let embeddings = result["data"]
            .as_array()
            .ok_or("Invalid response format")?
            .iter()
            .map(|item| {
                item["embedding"]
                    .as_array()
                    .unwrap()
                    .iter()
                    .map(|v| v.as_f64().unwrap_or(0.0) as f32)
                    .collect()
            })
            .collect();

        Ok(embeddings)
    }

    fn dimension(&self) -> usize {
        self.dimension
    }

    fn model_name(&self) -> &str {
        &self.model
    }
}

impl Clone for EmbeddingService {
    fn clone(&self) -> Self {
        Self {
            models: self.models.clone(),
            cache: self.cache.clone(),
        }
    }
}
```

#### 5.2.4 搜索与检索优化

**语义搜索实现：**
```rust
// src/services/vector_search_service.rs
use std::sync::Arc;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchRequest {
    pub query: String,
    pub knowledge_base_ids: Vec<String>,
    pub search_type: SearchType,
    pub limit: usize,
    pub threshold: f32,
    pub filters: Option<serde_json::Value>,
    pub rerank: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchType {
    Semantic,      // 纯语义搜索
    Keyword,       // 关键词搜索
    Hybrid,        // 混合搜索
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub chunk_id: String,
    pub document_id: String,
    pub knowledge_base_id: String,
    pub content: String,
    pub score: f32,
    pub metadata: serde_json::Value,
    pub highlights: Vec<String>,
}

pub struct VectorSearchService {
    chroma_client: Arc<chromadb::Client>,
    embedding_service: Arc<EmbeddingService>,
    reranker: Option<Arc<dyn Reranker>>,
}

impl VectorSearchService {
    pub fn new(
        chroma_client: Arc<chromadb::Client>,
        embedding_service: Arc<EmbeddingService>,
    ) -> Self {
        Self {
            chroma_client,
            embedding_service,
            reranker: None,
        }
    }

    // 执行搜索
    pub async fn search(&self, request: SearchRequest) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        match request.search_type {
            SearchType::Semantic => self.semantic_search(request).await,
            SearchType::Keyword => self.keyword_search(request).await,
            SearchType::Hybrid => self.hybrid_search(request).await,
        }
    }

    // 语义搜索
    async fn semantic_search(&self, request: SearchRequest) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        // 1. 向量化查询
        let query_embedding = self.embedding_service
            .embed(&request.query, "default")
            .await?;

        let mut all_results = Vec::new();

        // 2. 在每个知识库中搜索
        for kb_id in &request.knowledge_base_ids {
            let collection_name = format!("knowledge_base_{}", kb_id);

            // 获取集合
            let collection = self.chroma_client.get_collection(&collection_name)?;

            // 执行向量搜索
            let results = collection.query(
                query_embeddings: Some(vec![query_embedding.clone()]),
                n_results: Some(request.limit),
                where_clause: request.filters.clone(),
                include: Some(vec!["documents", "metadatas", "distances"]),
            )?;

            // 转换结果
            for (i, (document, metadata, distance)) in results.documents[0]
                .iter()
                .zip(results.metadatas[0].iter())
                .zip(results.distances[0].iter())
                .enumerate()
            {
                if *distance <= request.threshold {
                    all_results.push(SearchResult {
                        chunk_id: results.ids[0][i].clone(),
                        document_id: metadata.get("document_id")
                            .and_then(|v| v.as_str())
                            .unwrap_or("")
                            .to_string(),
                        knowledge_base_id: kb_id.clone(),
                        content: document.clone(),
                        score: 1.0 - distance, // 转换为相似度分数
                        metadata: metadata.clone(),
                        highlights: self.extract_highlights(&request.query, document),
                    });
                }
            }
        }

        // 3. 按分数排序
        all_results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        // 4. 重排序（如果启用）
        if request.rerank && self.reranker.is_some() {
            all_results = self.rerank_results(&request.query, all_results).await?;
        }

        // 5. 限制结果数量
        all_results.truncate(request.limit);

        Ok(all_results)
    }

    // 关键词搜索
    async fn keyword_search(&self, request: SearchRequest) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        // 使用SQLite的FTS进行关键词搜索
        // 这里需要与SQLite数据库交互
        todo!("实现关键词搜索")
    }

    // 混合搜索
    async fn hybrid_search(&self, request: SearchRequest) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        // 1. 并行执行语义搜索和关键词搜索
        let semantic_request = SearchRequest {
            search_type: SearchType::Semantic,
            ..request.clone()
        };

        let keyword_request = SearchRequest {
            search_type: SearchType::Keyword,
            ..request.clone()
        };

        let (semantic_results, keyword_results) = tokio::try_join!(
            self.semantic_search(semantic_request),
            self.keyword_search(keyword_request)
        )?;

        // 2. 合并和重排序结果
        let merged_results = self.merge_search_results(semantic_results, keyword_results, 0.7, 0.3)?;

        Ok(merged_results)
    }

    // 合并搜索结果
    fn merge_search_results(
        &self,
        semantic_results: Vec<SearchResult>,
        keyword_results: Vec<SearchResult>,
        semantic_weight: f32,
        keyword_weight: f32,
    ) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        let mut merged = std::collections::HashMap::new();

        // 添加语义搜索结果
        for result in semantic_results {
            let key = result.chunk_id.clone();
            merged.insert(key, SearchResult {
                score: result.score * semantic_weight,
                ..result
            });
        }

        // 合并关键词搜索结果
        for result in keyword_results {
            let key = result.chunk_id.clone();
            if let Some(existing) = merged.get_mut(&key) {
                // 如果已存在，合并分数
                existing.score += result.score * keyword_weight;
            } else {
                // 如果不存在，添加新结果
                merged.insert(key, SearchResult {
                    score: result.score * keyword_weight,
                    ..result
                });
            }
        }

        // 转换为向量并排序
        let mut results: Vec<SearchResult> = merged.into_values().collect();
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(results)
    }

    // 提取高亮片段
    fn extract_highlights(&self, query: &str, content: &str) -> Vec<String> {
        let query_terms: Vec<&str> = query.split_whitespace().collect();
        let mut highlights = Vec::new();

        for term in query_terms {
            if let Some(pos) = content.to_lowercase().find(&term.to_lowercase()) {
                let start = pos.saturating_sub(50);
                let end = std::cmp::min(pos + term.len() + 50, content.len());
                let highlight = content[start..end].to_string();
                highlights.push(highlight);
            }
        }

        highlights
    }

    // 重排序结果
    async fn rerank_results(&self, query: &str, results: Vec<SearchResult>) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>> {
        if let Some(reranker) = &self.reranker {
            reranker.rerank(query, results).await
        } else {
            Ok(results)
        }
    }
}

// 重排序器trait
#[async_trait::async_trait]
pub trait Reranker: Send + Sync {
    async fn rerank(&self, query: &str, results: Vec<SearchResult>) -> Result<Vec<SearchResult>, Box<dyn std::error::Error>>;
}
```

### 5.3 数据库关系图与数据流

#### 5.3.1 数据库关系图

**SQLite与ChromaDB关系图：**
```
AI Studio 数据库关系图：

┌─────────────────────────────────────────────────────────────┐
│                        SQLite关系型数据库                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  user_settings  │    │ chat_sessions   │                │
│  │                 │    │                 │                │
│  │ • id (PK)       │    │ • id (PK)       │                │
│  │ • key           │    │ • title         │                │
│  │ • value         │    │ • model_id (FK) │────────┐       │
│  │ • category      │    │ • system_prompt │        │       │
│  │ • created_at    │    │ • created_at    │        │       │
│  └─────────────────┘    └─────────────────┘        │       │
│                                   │                 │       │
│                                   │                 │       │
│                                   ▼                 ▼       │
│  ┌─────────────────┐    ┌─────────────────┐ ┌─────────────┐ │
│  │ chat_messages   │    │message_attachments│ │ ai_models   │ │
│  │                 │    │                 │ │             │ │
│  │ • id (PK)       │    │ • id (PK)       │ │ • id (PK)   │ │
│  │ • session_id(FK)│◄───│ • message_id(FK)│ │ • name      │ │
│  │ • role          │    │ • file_name     │ │ • model_type│ │
│  │ • content       │    │ • file_path     │ │ • file_path │ │
│  │ • token_count   │    │ • file_size     │ │ • status    │ │
│  │ • created_at    │    │ • processed     │ │ • created_at│ │
│  └─────────────────┘    └─────────────────┘ └─────────────┘ │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │knowledge_bases  │    │   documents     │                │
│  │                 │    │                 │                │
│  │ • id (PK)       │    │ • id (PK)       │                │
│  │ • name          │    │ • kb_id (FK)    │◄───────────────┤
│  │ • description   │    │ • file_name     │                │
│  │ • embedding_model│   │ • file_path     │                │
│  │ • collection_name│   │ • processing_status│             │
│  │ • created_at    │    │ • created_at    │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           │                       ▼                        │
│           │              ┌─────────────────┐               │
│           │              │document_chunks  │               │
│           │              │                 │               │
│           │              │ • id (PK)       │               │
│           │              │ • document_id(FK)│              │
│           │              │ • kb_id (FK)    │◄──────────────┤
│           │              │ • chunk_index   │               │
│           │              │ • content       │               │
│           │              │ • embedding_vector_id│          │
│           │              │ • created_at    │               │
│           │              └─────────────────┘               │
│           │                       │                        │
└───────────┼───────────────────────┼────────────────────────┘
            │                       │
            │                       │ embedding_vector_id
            │                       │
            ▼                       ▼
┌─────────────────────────────────────────────────────────────┐
│                        ChromaDB向量数据库                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Collection: knowledge_base_{kb_id}                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    向量集合                              │ │
│  │                                                         │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │   Vector    │  │  Metadata   │  │   Document  │     │ │
│  │  │             │  │             │  │             │     │ │
│  │  │ • id        │  │ • chunk_id  │  │ • content   │     │ │
│  │  │ • embedding │  │ • doc_id    │  │ • text      │     │ │
│  │  │ • dimension │  │ • kb_id     │  │ • processed │     │ │
│  │  │ • created_at│  │ • chunk_idx │  │ • metadata  │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  │                                                         │ │
│  │  HNSW索引: 快速相似度搜索                                │ │
│  │  距离度量: cosine/euclidean/inner_product                │ │
│  │  维度: 1536 (OpenAI) / 768 (BERT) / 其他                │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  Collection: chat_history_embeddings                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    聊天历史向量                          │ │
│  │                                                         │ │
│  │  • 用户查询向量                                          │ │
│  │  • AI回复向量                                           │ │
│  │  • 会话上下文向量                                        │ │
│  │  • 相似对话检索                                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 5.3.2 数据流设计

**核心数据流图：**
```
AI Studio 核心数据流：

┌─────────────────────────────────────────────────────────────┐
│                        用户交互层                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  用户操作 ──► 前端界面 ──► 事件处理 ──► API调用              │
│     │           │           │           │                   │
│     ▼           ▼           ▼           ▼                   │
│  文件上传    聊天输入    搜索查询    配置修改                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ Tauri IPC
┌─────────────────────────────────────────────────────────────┐
│                        业务逻辑层                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 系统服务 │ │
│  │             │ │             │ │             │ │         │ │
│  │ 1.接收消息   │ │ 1.文档上传   │ │ 1.模型加载   │ │ 1.配置管理│ │
│  │ 2.上下文检索 │ │ 2.内容解析   │ │ 2.推理执行   │ │ 2.日志记录│ │
│  │ 3.AI推理    │ │ 3.分块处理   │ │ 3.结果返回   │ │ 3.性能监控│ │
│  │ 4.响应生成   │ │ 4.向量化    │ │ 4.资源管理   │ │ 4.错误处理│ │
│  │ 5.历史保存   │ │ 5.索引构建   │ │             │ │         │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│         │               │               │           │       │
│         ▼               ▼               ▼           ▼       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    数据访问层                            │ │
│  │                                                         │ │
│  │  • 数据验证与转换                                        │ │
│  │  • 事务管理                                             │ │
│  │  • 缓存策略                                             │ │
│  │  • 连接池管理                                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────┐         ┌─────────────────────┐     │
│  │    SQLite数据库     │         │   ChromaDB向量库    │     │
│  │                     │         │                     │     │
│  │  写入流程:          │         │  写入流程:          │     │
│  │  1.数据验证         │         │  1.文本分块         │     │
│  │  2.事务开始         │         │  2.向量化处理       │     │
│  │  3.SQL执行          │         │  3.向量存储         │     │
│  │  4.索引更新         │         │  4.索引构建         │     │
│  │  5.事务提交         │         │  5.元数据关联       │     │
│  │                     │         │                     │     │
│  │  读取流程:          │         │  读取流程:          │     │
│  │  1.查询解析         │         │  1.查询向量化       │     │
│  │  2.索引查找         │         │  2.相似度计算       │     │
│  │  3.数据获取         │         │  3.结果排序         │     │
│  │  4.结果缓存         │         │  4.元数据补充       │     │
│  │  5.数据返回         │         │  5.结果返回         │     │
│  └─────────────────────┘         └─────────────────────┘     │
│           │                               │                 │
│           ▼                               ▼                 │
│  ┌─────────────────────┐         ┌─────────────────────┐     │
│  │    文件系统存储     │         │    内存缓存系统     │     │
│  │                     │         │                     │     │
│  │  • 模型文件         │         │  • 查询结果缓存     │     │
│  │  • 文档文件         │         │  • 向量缓存         │     │
│  │  • 配置文件         │         │  • 会话缓存         │     │
│  │  • 日志文件         │         │  • 模型缓存         │     │
│  │  • 临时文件         │         │  • 元数据缓存       │     │
│  └─────────────────────┘         └─────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

#### 5.3.3 数据同步与一致性

**数据一致性保证机制：**
```rust
// src/database/consistency_manager.rs
use std::sync::Arc;
use tokio::sync::Mutex;
use sqlx::SqlitePool;

pub struct ConsistencyManager {
    sqlite_pool: Arc<SqlitePool>,
    chroma_client: Arc<chromadb::Client>,
    sync_queue: Arc<Mutex<Vec<SyncTask>>>,
}

#[derive(Debug, Clone)]
pub struct SyncTask {
    pub task_type: SyncTaskType,
    pub entity_id: String,
    pub data: serde_json::Value,
    pub created_at: i64,
    pub retry_count: u32,
}

#[derive(Debug, Clone)]
pub enum SyncTaskType {
    DocumentAdd,
    DocumentUpdate,
    DocumentDelete,
    ChunkAdd,
    ChunkUpdate,
    ChunkDelete,
    CollectionCreate,
    CollectionDelete,
}

impl ConsistencyManager {
    pub fn new(sqlite_pool: Arc<SqlitePool>, chroma_client: Arc<chromadb::Client>) -> Self {
        Self {
            sqlite_pool,
            chroma_client,
            sync_queue: Arc::new(Mutex::new(Vec::new())),
        }
    }

    // 启动同步服务
    pub async fn start_sync_service(&self) -> Result<(), Box<dyn std::error::Error>> {
        let manager = self.clone();

        tokio::spawn(async move {
            loop {
                if let Err(e) = manager.process_sync_queue().await {
                    eprintln!("同步队列处理错误: {}", e);
                }

                // 每5秒处理一次同步队列
                tokio::time::sleep(std::time::Duration::from_secs(5)).await;
            }
        });

        Ok(())
    }

    // 处理同步队列
    async fn process_sync_queue(&self) -> Result<(), Box<dyn std::error::Error>> {
        let mut queue = self.sync_queue.lock().await;
        let mut completed_tasks = Vec::new();

        for (index, task) in queue.iter_mut().enumerate() {
            match self.execute_sync_task(task).await {
                Ok(_) => {
                    completed_tasks.push(index);
                }
                Err(e) => {
                    eprintln!("同步任务执行失败: {} - {}", task.entity_id, e);
                    task.retry_count += 1;

                    // 超过最大重试次数则丢弃
                    if task.retry_count > 3 {
                        completed_tasks.push(index);
                    }
                }
            }
        }

        // 移除已完成的任务（从后往前移除避免索引问题）
        for &index in completed_tasks.iter().rev() {
            queue.remove(index);
        }

        Ok(())
    }

    // 执行同步任务
    async fn execute_sync_task(&self, task: &SyncTask) -> Result<(), Box<dyn std::error::Error>> {
        match task.task_type {
            SyncTaskType::DocumentAdd => {
                self.sync_document_add(&task.entity_id, &task.data).await?;
            }
            SyncTaskType::DocumentDelete => {
                self.sync_document_delete(&task.entity_id).await?;
            }
            SyncTaskType::ChunkAdd => {
                self.sync_chunk_add(&task.entity_id, &task.data).await?;
            }
            SyncTaskType::ChunkDelete => {
                self.sync_chunk_delete(&task.entity_id).await?;
            }
            _ => {
                // 其他同步任务的实现
            }
        }

        Ok(())
    }

    // 同步文档添加
    async fn sync_document_add(&self, document_id: &str, data: &serde_json::Value) -> Result<(), Box<dyn std::error::Error>> {
        // 1. 从SQLite获取文档信息
        let document = sqlx::query_as::<_, Document>(
            "SELECT * FROM documents WHERE id = ?"
        )
        .bind(document_id)
        .fetch_one(&*self.sqlite_pool)
        .await?;

        // 2. 获取文档的所有分块
        let chunks = sqlx::query_as::<_, DocumentChunk>(
            "SELECT * FROM document_chunks WHERE document_id = ?"
        )
        .bind(document_id)
        .fetch_all(&*self.sqlite_pool)
        .await?;

        // 3. 在ChromaDB中创建或更新向量
        let collection_name = format!("knowledge_base_{}", document.knowledge_base_id);
        let collection = self.chroma_client.get_or_create_collection(&collection_name)?;

        for chunk in chunks {
            // 向量化分块内容
            let embedding = self.generate_embedding(&chunk.content).await?;

            // 添加到ChromaDB
            collection.add(
                ids: vec![chunk.id.clone()],
                embeddings: Some(vec![embedding]),
                documents: Some(vec![chunk.content.clone()]),
                metadatas: Some(vec![serde_json::json!({
                    "document_id": document.id,
                    "knowledge_base_id": document.knowledge_base_id,
                    "chunk_index": chunk.chunk_index,
                    "chunk_type": chunk.chunk_type,
                    "created_at": chunk.created_at
                })]),
            )?;

            // 更新SQLite中的向量ID
            sqlx::query(
                "UPDATE document_chunks SET embedding_vector_id = ? WHERE id = ?"
            )
            .bind(&chunk.id)
            .bind(&chunk.id)
            .execute(&*self.sqlite_pool)
            .await?;
        }

        Ok(())
    }

    // 同步文档删除
    async fn sync_document_delete(&self, document_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        // 1. 获取文档信息
        let document = sqlx::query_as::<_, Document>(
            "SELECT * FROM documents WHERE id = ?"
        )
        .bind(document_id)
        .fetch_optional(&*self.sqlite_pool)
        .await?;

        if let Some(doc) = document {
            // 2. 获取所有分块ID
            let chunk_ids: Vec<String> = sqlx::query_scalar(
                "SELECT id FROM document_chunks WHERE document_id = ?"
            )
            .bind(document_id)
            .fetch_all(&*self.sqlite_pool)
            .await?;

            // 3. 从ChromaDB删除向量
            if !chunk_ids.is_empty() {
                let collection_name = format!("knowledge_base_{}", doc.knowledge_base_id);
                let collection = self.chroma_client.get_collection(&collection_name)?;
                collection.delete(ids: Some(chunk_ids))?;
            }

            // 4. 从SQLite删除记录（级联删除会自动处理分块）
            sqlx::query("DELETE FROM documents WHERE id = ?")
                .bind(document_id)
                .execute(&*self.sqlite_pool)
                .await?;
        }

        Ok(())
    }

    // 生成向量嵌入
    async fn generate_embedding(&self, text: &str) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
        // 这里应该调用实际的嵌入服务
        // 简化实现，返回随机向量
        Ok(vec![0.0; 1536])
    }

    // 添加同步任务
    pub async fn add_sync_task(&self, task: SyncTask) {
        let mut queue = self.sync_queue.lock().await;
        queue.push(task);
    }

    // 数据一致性检查
    pub async fn check_consistency(&self) -> Result<ConsistencyReport, Box<dyn std::error::Error>> {
        let mut report = ConsistencyReport::new();

        // 1. 检查SQLite中的文档是否在ChromaDB中有对应向量
        let documents = sqlx::query_as::<_, Document>(
            "SELECT * FROM documents WHERE processing_status = 'completed'"
        )
        .fetch_all(&*self.sqlite_pool)
        .await?;

        for doc in documents {
            let collection_name = format!("knowledge_base_{}", doc.knowledge_base_id);

            // 检查集合是否存在
            if let Ok(collection) = self.chroma_client.get_collection(&collection_name) {
                // 检查文档的分块是否都有向量
                let chunk_ids: Vec<String> = sqlx::query_scalar(
                    "SELECT id FROM document_chunks WHERE document_id = ?"
                )
                .bind(&doc.id)
                .fetch_all(&*self.sqlite_pool)
                .await?;

                for chunk_id in chunk_ids {
                    if let Err(_) = collection.get(ids: vec![chunk_id.clone()]) {
                        report.missing_vectors.push(chunk_id);
                    }
                }
            } else {
                report.missing_collections.push(collection_name);
            }
        }

        // 2. 检查ChromaDB中的向量是否在SQLite中有对应记录
        // 这需要遍历所有集合，实现较复杂，这里简化

        Ok(report)
    }
}

#[derive(Debug)]
pub struct ConsistencyReport {
    pub missing_vectors: Vec<String>,
    pub missing_collections: Vec<String>,
    pub orphaned_vectors: Vec<String>,
    pub inconsistent_metadata: Vec<String>,
}

impl ConsistencyReport {
    pub fn new() -> Self {
        Self {
            missing_vectors: Vec::new(),
            missing_collections: Vec::new(),
            orphaned_vectors: Vec::new(),
            inconsistent_metadata: Vec::new(),
        }
    }

    pub fn is_consistent(&self) -> bool {
        self.missing_vectors.is_empty()
            && self.missing_collections.is_empty()
            && self.orphaned_vectors.is_empty()
            && self.inconsistent_metadata.is_empty()
    }
}

impl Clone for ConsistencyManager {
    fn clone(&self) -> Self {
        Self {
            sqlite_pool: self.sqlite_pool.clone(),
            chroma_client: self.chroma_client.clone(),
            sync_queue: self.sync_queue.clone(),
        }
    }
}

// 数据结构定义
#[derive(Debug, sqlx::FromRow)]
struct Document {
    id: String,
    knowledge_base_id: String,
    file_name: String,
    processing_status: String,
}

#[derive(Debug, sqlx::FromRow)]
struct DocumentChunk {
    id: String,
    document_id: String,
    knowledge_base_id: String,
    chunk_index: i32,
    content: String,
    chunk_type: String,
    created_at: String,
}
```

### 5.4 数据结构定义

#### 5.4.1 核心数据结构

**Rust数据结构定义：**
```rust
// src/types/mod.rs - 核心数据类型定义
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;

// ===================================================
// 聊天相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ChatSession {
    pub id: String,
    pub title: String,
    pub model_id: String,
    pub system_prompt: Option<String>,
    pub temperature: f32,
    pub max_tokens: i32,
    pub top_p: f32,
    pub frequency_penalty: f32,
    pub presence_penalty: f32,
    pub enable_rag: bool,
    pub knowledge_bases: Option<String>, // JSON数组
    pub message_count: i32,
    pub total_tokens: i32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub is_archived: bool,
    pub tags: Option<String>, // JSON数组
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ChatMessage {
    pub id: String,
    pub session_id: String,
    pub role: MessageRole,
    pub content: String,
    pub content_type: ContentType,
    pub metadata: Option<String>, // JSON
    pub token_count: i32,
    pub model_used: Option<String>,
    pub inference_time_ms: Option<i32>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub is_deleted: bool,
    pub parent_message_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ContentType {
    Text,
    Image,
    File,
    Audio,
    Video,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MessageAttachment {
    pub id: i64,
    pub message_id: String,
    pub file_name: String,
    pub file_path: String,
    pub file_size: i64,
    pub file_type: String,
    pub mime_type: Option<String>,
    pub file_hash: Option<String>,
    pub processed: bool,
    pub processing_result: Option<String>, // JSON
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// ===================================================
// 模型相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AIModel {
    pub id: String,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub model_type: ModelType,
    pub architecture: Option<String>,
    pub parameter_count: Option<String>,
    pub quantization: Option<String>,
    pub file_path: Option<String>,
    pub file_size: Option<i64>,
    pub file_hash: Option<String>,
    pub config_path: Option<String>,
    pub tokenizer_path: Option<String>,
    pub inference_engine: InferenceEngine,
    pub supported_features: Option<String>, // JSON数组
    pub performance_metrics: Option<String>, // JSON
    pub hardware_requirements: Option<String>, // JSON
    pub license: Option<String>,
    pub author: Option<String>,
    pub version: Option<String>,
    pub download_url: Option<String>,
    pub status: ModelStatus,
    pub is_local: bool,
    pub is_enabled: bool,
    pub download_progress: f32,
    pub load_time_ms: Option<i32>,
    pub memory_usage_mb: Option<i32>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub usage_count: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ModelType {
    LLM,
    Embedding,
    Multimodal,
    TTS,
    ASR,
    ImageGeneration,
    CodeGeneration,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum InferenceEngine {
    Candle,
    LlamaCpp,
    ONNX,
    TensorRT,
    OpenAI,
    Anthropic,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ModelStatus {
    Available,
    Downloading,
    Loading,
    Loaded,
    Error,
    Disabled,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ModelDownloadTask {
    pub id: String,
    pub model_id: String,
    pub download_url: String,
    pub file_path: String,
    pub total_size: i64,
    pub downloaded_size: i64,
    pub status: DownloadStatus,
    pub progress: f32,
    pub download_speed: i32,
    pub error_message: Option<String>,
    pub retry_count: i32,
    pub chunks_info: Option<String>, // JSON
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

// ===================================================
// 知识库相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct KnowledgeBase {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub embedding_model: String,
    pub chunk_size: i32,
    pub chunk_overlap: i32,
    pub collection_name: String,
    pub document_count: i32,
    pub chunk_count: i32,
    pub total_size: i64,
    pub index_status: IndexStatus,
    pub last_indexed: Option<chrono::DateTime<chrono::Utc>>,
    pub settings: Option<String>, // JSON
    pub permissions: Option<String>, // JSON
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub is_public: bool,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum IndexStatus {
    Building,
    Ready,
    Error,
    Updating,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Document {
    pub id: String,
    pub knowledge_base_id: String,
    pub file_name: String,
    pub file_path: String,
    pub file_size: i64,
    pub file_type: String,
    pub file_hash: String,
    pub mime_type: Option<String>,
    pub title: Option<String>,
    pub author: Option<String>,
    pub language: String,
    pub page_count: Option<i32>,
    pub word_count: Option<i32>,
    pub content_preview: Option<String>,
    pub metadata: Option<String>, // JSON
    pub processing_status: ProcessingStatus,
    pub processing_progress: f32,
    pub processing_error: Option<String>,
    pub chunk_count: i32,
    pub indexed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub is_deleted: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ProcessingStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct DocumentChunk {
    pub id: String,
    pub document_id: String,
    pub knowledge_base_id: String,
    pub chunk_index: i32,
    pub content: String,
    pub content_hash: String,
    pub start_pos: Option<i32>,
    pub end_pos: Option<i32>,
    pub chunk_type: ChunkType,
    pub metadata: Option<String>, // JSON
    pub token_count: Option<i32>,
    pub embedding_vector_id: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum ChunkType {
    Text,
    Heading,
    List,
    Table,
    Code,
    Image,
}

// ===================================================
// 网络相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct NetworkDevice {
    pub id: String,
    pub name: String,
    pub device_type: DeviceType,
    pub ip_address: String,
    pub port: i32,
    pub version: Option<String>,
    pub capabilities: Option<String>, // JSON数组
    pub status: DeviceStatus,
    pub trust_level: TrustLevel,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
    pub connection_count: i32,
    pub data_transferred: i64,
    pub public_key: Option<String>,
    pub device_info: Option<String>, // JSON
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum DeviceType {
    AIStudio,
    Desktop,
    Mobile,
    Server,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum DeviceStatus {
    Online,
    Offline,
    Connecting,
    Connected,
    Busy,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TrustLevel {
    Unknown,
    Trusted,
    Blocked,
}

// ===================================================
// 插件相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Plugin {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: Option<String>,
    pub author: Option<String>,
    pub homepage: Option<String>,
    pub repository: Option<String>,
    pub license: Option<String>,
    pub entry_point: String,
    pub manifest_path: String,
    pub install_path: String,
    pub file_size: Option<i64>,
    pub permissions: Option<String>, // JSON数组
    pub dependencies: Option<String>, // JSON
    pub config_schema: Option<String>, // JSON Schema
    pub default_config: Option<String>, // JSON
    pub user_config: Option<String>, // JSON
    pub status: PluginStatus,
    pub is_system: bool,
    pub is_verified: bool,
    pub download_count: i32,
    pub rating: f32,
    pub review_count: i32,
    pub last_error: Option<String>,
    pub performance_metrics: Option<String>, // JSON
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub installed_at: chrono::DateTime<chrono::Utc>,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum PluginStatus {
    Installed,
    Enabled,
    Disabled,
    Error,
    Updating,
}

// ===================================================
// 系统相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SystemLog {
    pub id: i64,
    pub level: LogLevel,
    pub category: LogCategory,
    pub message: String,
    pub details: Option<String>, // JSON
    pub source: Option<String>,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub request_id: Option<String>,
    pub stack_trace: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
    Fatal,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum LogCategory {
    System,
    Chat,
    Knowledge,
    Model,
    Network,
    Plugin,
    Database,
    Security,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PerformanceMetric {
    pub id: i64,
    pub metric_name: String,
    pub metric_type: MetricType,
    pub value: f64,
    pub unit: Option<String>,
    pub tags: Option<String>, // JSON
    pub component: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum MetricType {
    Counter,
    Gauge,
    Histogram,
    Timer,
}

// ===================================================
// API请求/响应数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<ApiError>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
    pub total_pages: i32,
}

// ===================================================
// 配置相关数据结构
// ===================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub models: ModelConfig,
    pub network: NetworkConfig,
    pub security: SecurityConfig,
    pub ui: UIConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub sqlite_path: String,
    pub chromadb_path: String,
    pub connection_pool_size: u32,
    pub query_timeout_seconds: u64,
    pub backup_enabled: bool,
    pub backup_interval_hours: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub models_directory: String,
    pub default_embedding_model: String,
    pub max_concurrent_downloads: u32,
    pub download_chunk_size: u64,
    pub model_cache_size_mb: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub enable_lan_discovery: bool,
    pub discovery_port: u16,
    pub file_transfer_port: u16,
    pub max_connections: u32,
    pub connection_timeout_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_encryption: bool,
    pub key_derivation_iterations: u32,
    pub session_timeout_minutes: u32,
    pub max_login_attempts: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UIConfig {
    pub theme: String,
    pub language: String,
    pub font_size: u32,
    pub enable_animations: bool,
    pub auto_save_interval_seconds: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: LogLevel,
    pub max_file_size_mb: u64,
    pub max_files: u32,
    pub enable_console_output: bool,
    pub enable_file_output: bool,
}
```
